import { app, BrowserWindow, ipc<PERSON>ain, IpcMainInvokeEvent, dialog } from 'electron'
import { autoUpdater } from 'electron-updater'
import path from 'path'
import { isDev } from './utils'
import { DatabaseManager } from './database'
import { FileSystemManager } from './fileSystem'

class App {
  public mainWindow: BrowserWindow | null = null
  private db: DatabaseManager
  private fileSystem: FileSystemManager

  constructor() {
    this.db = new DatabaseManager()
    this.fileSystem = new FileSystemManager(this.db)
  }

  private createWindow(): void {
    // Following Electron best practices from official documentation
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      show: false, // Don't show until ready-to-show event
      frame: false, // Frameless window for custom chrome
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'hidden',
      titleBarOverlay: false,
      backgroundColor: '#111827', // Match our app's dark theme (gray-900)
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false,
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: true,
      },
    })

    // Use ready-to-show event for graceful window display (Electron best practice)
    this.mainWindow.once('ready-to-show', () => {
      if (this.mainWindow) {
        this.mainWindow.show()

        // Open DevTools in development after window is shown
        if (isDev) {
          this.mainWindow.webContents.openDevTools()
        }
      }
    })

    if (isDev) {
      this.mainWindow.loadURL('http://localhost:5173')
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
    }

    this.mainWindow.on('closed', () => {
      this.mainWindow = null
    })
  }

  private validateSender(frame: any): boolean {
    // Validate the sender is from your app
    if (!frame || !frame.url) return false
    return frame.url.startsWith('file://') ||
           frame.url.startsWith('http://localhost:5173')
  }

  private validateInput(value: any, type: string, maxLength?: number): boolean {
    if (value === null || value === undefined) return false
    if (typeof value !== type) return false
    if (type === 'string' && maxLength && value.length > maxLength) return false
    return true
  }

  private async createContextStructure(contextPath: string, contextName: string): Promise<void> {
    const fs = await import('fs')
    const path = await import('path')

    // Create context directory
    await fs.promises.mkdir(contextPath, { recursive: true })

    // Create subdirectories
    await fs.promises.mkdir(path.join(contextPath, 'documents'), { recursive: true })
    await fs.promises.mkdir(path.join(contextPath, 'images'), { recursive: true })
    await fs.promises.mkdir(path.join(contextPath, 'artifacts'), { recursive: true })
    await fs.promises.mkdir(path.join(contextPath, '.context'), { recursive: true })

    // Create master.md
    const masterContent = `# ${contextName}

Welcome to your intelligent context vault! This is your master document that serves as the central hub for organizing and understanding your project.

## Overview
This context vault helps you organize files, conversations, and AI insights in one place.

## Quick Start
1. **Add files** to the \`documents/\` folder to get started
2. **Start a conversation** using the chat interface with this context selected
3. **Organize insights** and progress notes right here in this document

## How It Works
- 📁 **Documents folder**: Store your project files here
- 🖼️ **Images folder**: Add screenshots, diagrams, and visual assets
- 🎯 **Artifacts folder**: Save important outputs from AI conversations
- 🧠 **AI Memory**: The \`.context/\` folder contains AI memory optimized for Gemma models

## AI Insights
*This section will be automatically updated as you add files and have conversations*

## Project Progress
*Use this space to track your progress and key milestones*

---
*Last updated: ${new Date().toLocaleString()}*
*Files: 0 | Conversations: 0*`

    await fs.promises.writeFile(path.join(contextPath, 'master.md'), masterContent, 'utf8')

    // Create context metadata
    const metadata = {
      id: contextName.toLowerCase().replace(/\s+/g, '-'),
      name: contextName,
      created: new Date().toISOString(),
      description: `Context vault for ${contextName}`,
      contextType: 'getting-started'
    }

    await fs.promises.writeFile(
      path.join(contextPath, '.context', 'metadata.json'),
      JSON.stringify(metadata, null, 2),
      'utf8'
    )

    // Create AI memory file
    const memoryContent = `# AI Memory for ${contextName}

## Context Understanding
This context vault was just created and is ready for your first project.

## Key Concepts
*AI will learn and document key concepts from your files and conversations*

## Relationships
*Connections between files, ideas, and conversations will be tracked here*

## Memory Chunks
*Optimized memory chunks for Gemma models will be stored here*

---
*This file is automatically managed by ChatLo's AI system*`

    await fs.promises.writeFile(path.join(contextPath, '.context', 'ai-memory.md'), memoryContent, 'utf8')

    // Create chat links file
    await fs.promises.writeFile(
      path.join(contextPath, '.context', 'chat-links.json'),
      JSON.stringify({ conversations: [] }, null, 2),
      'utf8'
    )
  }

  private setupIPC(): void {
    // Database operations with validation
    ipcMain.handle('db:getConversations', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return this.db.getConversations()
    })

    ipcMain.handle('db:getConversation', (event: IpcMainInvokeEvent, id: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
      return this.db.getConversation(id)
    })

    ipcMain.handle('db:createConversation', (event: IpcMainInvokeEvent, title: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(title, 'string', 200)) throw new Error('Invalid conversation title')
      console.log('Main: Creating conversation with title:', title)
      const id = this.db.createConversation(title)
      console.log('Main: Created conversation:', id)
      return id
    })

    ipcMain.handle('db:updateConversation', (event: IpcMainInvokeEvent, id: string, title: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
      if (!this.validateInput(title, 'string', 200)) throw new Error('Invalid conversation title')
      return this.db.updateConversation(id, title)
    })

    ipcMain.handle('db:deleteConversation', (event: IpcMainInvokeEvent, id: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
      return this.db.deleteConversation(id)
    })

    ipcMain.handle('db:addMessage', (event: IpcMainInvokeEvent, conversationId: string, message: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
      if (!message || typeof message !== 'object') throw new Error('Invalid message object')
      return this.db.addMessage(conversationId, message)
    })

    ipcMain.handle('db:togglePinMessage', (event: IpcMainInvokeEvent, messageId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      this.db.togglePinMessage(messageId)
    })

    ipcMain.handle('db:getMessages', (event: IpcMainInvokeEvent, conversationId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
      return this.db.getMessages(conversationId)
    })

    ipcMain.handle('db:searchConversations', (event: IpcMainInvokeEvent, searchTerm: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(searchTerm, 'string', 200)) throw new Error('Invalid search term')
      return this.db.searchConversationsAndMessages(searchTerm)
    })

    ipcMain.handle('db:getConversationsWithArtifacts', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return this.db.getConversationsWithArtifacts()
    })

    // Settings with validation
    ipcMain.handle('settings:get', (event: IpcMainInvokeEvent, key: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(key, 'string', 50)) throw new Error('Invalid settings key')
      return this.db.getSetting(key)
    })

    ipcMain.handle('settings:set', (event: IpcMainInvokeEvent, key: string, value: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(key, 'string', 50)) throw new Error('Invalid settings key')
      this.db.setSetting(key, value)
      console.log('Settings updated:', key, value)
    })

    // File system operations with validation
    ipcMain.handle('files:getChatloFolderPath', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return this.fileSystem.getChatloFolderPath()
    })

    ipcMain.handle('files:setChatloFolderPath', async (event: IpcMainInvokeEvent, path: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(path, 'string', 500)) throw new Error('Invalid folder path')
      return await this.fileSystem.setChatloFolderPath(path)
    })

    ipcMain.handle('files:getIndexedFiles', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null

      // Check if vault system is configured
      const vaultRootPath = this.db.getSetting('vault-root-path')
      console.log('[ELECTRON] files:getIndexedFiles called, vault root path:', vaultRootPath)

      if (vaultRootPath) {
        console.log('[ELECTRON] Vault system detected, returning empty array for legacy file system')
        return []
      }

      console.log('[ELECTRON] No vault system, using legacy file system')
      return this.fileSystem.getIndexedFiles()
    })

    ipcMain.handle('files:searchFiles', (event: IpcMainInvokeEvent, query: string, limit?: number) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(query, 'string', 200)) throw new Error('Invalid search query')

      // Check if vault system is configured
      const vaultRootPath = this.db.getSetting('vault-root-path')
      console.log('[ELECTRON] files:searchFiles called, vault root path:', vaultRootPath)

      if (vaultRootPath) {
        console.log('[ELECTRON] Vault system detected, returning empty array for legacy file search')
        return []
      }

      console.log('[ELECTRON] No vault system, using legacy file search')
      const searchLimit = limit && limit > 0 && limit <= 50 ? limit : 10 // Max 50 results
      return this.fileSystem.searchFiles(query, searchLimit)
    })

    ipcMain.handle('files:processFileContent', (event: IpcMainInvokeEvent, fileId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(fileId, 'string', 100)) throw new Error('Invalid file ID')

      // Check if vault system is configured
      const vaultRootPath = this.db.getSetting('vault-root-path')
      if (vaultRootPath) {
        console.log('[ELECTRON] Vault system detected, legacy file processing not available')
        return false
      }

      return this.fileSystem.processFileContent(fileId)
    })

    ipcMain.handle('files:indexFile', async (event: IpcMainInvokeEvent, filePath: string, processContent?: boolean) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')

      // Check if vault system is configured
      const vaultRootPath = this.db.getSetting('vault-root-path')
      if (vaultRootPath) {
        console.log('[ELECTRON] Vault system detected, legacy file indexing not available')
        return null
      }

      return await this.fileSystem.indexFile(filePath, processContent || false)
    })

    ipcMain.handle('files:indexAllFiles', async (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null

      // Check if vault system is configured
      const vaultRootPath = this.db.getSetting('vault-root-path')
      if (vaultRootPath) {
        console.log('[ELECTRON] Vault system detected, legacy file indexing not available')
        return
      }

      return await this.fileSystem.indexAllFiles()
    })

    ipcMain.handle('files:copyFileToUploads', async (event: IpcMainInvokeEvent, sourcePath: string, filename?: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(sourcePath, 'string', 500)) throw new Error('Invalid source path')
      if (filename && !this.validateInput(filename, 'string', 255)) throw new Error('Invalid filename')

      // Check if vault system is configured
      const vaultRootPath = this.db.getSetting('vault-root-path')
      if (vaultRootPath) {
        console.log('[ELECTRON] Vault system detected, legacy file copy not available')
        return null
      }

      return await this.fileSystem.copyFileToUploads(sourcePath, filename)
    })

    // File attachment operations (still needed for database operations)
    ipcMain.handle('files:addFileAttachment', (event: IpcMainInvokeEvent, messageId: string, fileId: string, attachmentType: 'attachment' | 'reference') => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      if (!this.validateInput(fileId, 'string', 100)) throw new Error('Invalid file ID')
      if (!['attachment', 'reference'].includes(attachmentType)) throw new Error('Invalid attachment type')
      return this.db.addFileAttachment(messageId, fileId, attachmentType)
    })

    ipcMain.handle('files:getFileAttachments', (event: IpcMainInvokeEvent, messageId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      return this.db.getFileAttachments(messageId)
    })

    ipcMain.handle('files:getMessageFiles', (event: IpcMainInvokeEvent, messageId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      return this.db.getMessageFiles(messageId)
    })

    ipcMain.handle('files:removeFileAttachment', (event: IpcMainInvokeEvent, attachmentId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(attachmentId, 'string', 100)) throw new Error('Invalid attachment ID')
      this.db.removeFileAttachment(attachmentId)
    })

    ipcMain.handle('files:saveContentAsFile', async (event: IpcMainInvokeEvent, content: string, filename: string, subfolder?: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(content, 'string', 1000000)) throw new Error('Content too large')
      if (!this.validateInput(filename, 'string', 255)) throw new Error('Invalid filename')
      if (subfolder && !this.validateInput(subfolder, 'string', 100)) throw new Error('Invalid subfolder')

      // Check if vault system is configured
      const vaultRootPath = this.db.getSetting('vault-root-path')
      if (vaultRootPath) {
        console.log('[ELECTRON] Vault system detected, legacy file save not available')
        return null
      }

      return await this.fileSystem.saveContentAsFile(content, filename, subfolder)
    })

    ipcMain.handle('files:deleteFile', async (event: IpcMainInvokeEvent, fileId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(fileId, 'string', 100)) throw new Error('Invalid file ID')

      // Check if vault system is configured
      const vaultRootPath = this.db.getSetting('vault-root-path')
      if (vaultRootPath) {
        console.log('[ELECTRON] Vault system detected, legacy file delete not available')
        return false
      }

      return await this.fileSystem.deleteFile(fileId)
    })

    ipcMain.handle('files:getFileContent', async (event: IpcMainInvokeEvent, filePath: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')

      // Check if vault system is configured
      const vaultRootPath = this.db.getSetting('vault-root-path')
      if (vaultRootPath) {
        console.log('[ELECTRON] Vault system detected, legacy file content not available')
        return null
      }

      return this.fileSystem.getFileContent(filePath)
    })

    ipcMain.handle('files:fileExists', (event: IpcMainInvokeEvent, filePath: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')

      // Check if vault system is configured
      const vaultRootPath = this.db.getSetting('vault-root-path')
      if (vaultRootPath) {
        console.log('[ELECTRON] Vault system detected, legacy file exists check not available')
        return false
      }

      return this.fileSystem.fileExists(filePath)
    })

    ipcMain.handle('files:showOpenDialog', async (event: IpcMainInvokeEvent, options: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      return await dialog.showOpenDialog(this.mainWindow!, options)
    })

    ipcMain.handle('files:showSaveDialog', async (event: IpcMainInvokeEvent, options: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      return await dialog.showSaveDialog(this.mainWindow!, options)
    })

    // Vault IPC handlers
    ipcMain.handle('vault:createDirectory', async (event: IpcMainInvokeEvent, dirPath: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized' }
      if (!this.validateInput(dirPath, 'string', 500)) return { success: false, error: 'Invalid directory path' }

      try {
        const fs = await import('fs')
        await fs.promises.mkdir(dirPath, { recursive: true })
        return { success: true }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    })

    ipcMain.handle('vault:writeFile', async (event: IpcMainInvokeEvent, filePath: string, content: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized' }
      if (!this.validateInput(filePath, 'string', 500)) return { success: false, error: 'Invalid file path' }
      if (!this.validateInput(content, 'string', 1000000)) return { success: false, error: 'Content too large' }

      try {
        const fs = await import('fs')
        await fs.promises.writeFile(filePath, content, 'utf8')
        return { success: true }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    })

    ipcMain.handle('vault:readDirectory', async (event: IpcMainInvokeEvent, dirPath: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized' }
      if (!this.validateInput(dirPath, 'string', 500)) return { success: false, error: 'Invalid directory path' }

      try {
        const fs = await import('fs')
        const path = await import('path')
        const items = await fs.promises.readdir(dirPath, { withFileTypes: true })

        const result = await Promise.all(items.map(async (item) => {
          const itemPath = path.join(dirPath, item.name)
          const stats = await fs.promises.stat(itemPath)

          return {
            name: item.name,
            path: itemPath,
            isDirectory: item.isDirectory(),
            size: item.isFile() ? stats.size : undefined,
            modified: stats.mtime.toISOString()
          }
        }))

        return { success: true, items: result }
      } catch (error: any) {
        return { success: false, error: error.message, items: [] }
      }
    })

    ipcMain.handle('vault:removeDirectory', async (event: IpcMainInvokeEvent, dirPath: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized' }
      if (!this.validateInput(dirPath, 'string', 500)) return { success: false, error: 'Invalid directory path' }

      try {
        const fs = await import('fs')
        await fs.promises.rm(dirPath, { recursive: true, force: true })
        return { success: true }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    })

    ipcMain.handle('vault:pathExists', async (event: IpcMainInvokeEvent, targetPath: string) => {
      if (!this.validateSender(event.senderFrame)) return { exists: false, error: 'Unauthorized' }
      if (!this.validateInput(targetPath, 'string', 500)) return { exists: false, error: 'Invalid path' }

      try {
        const fs = await import('fs')
        await fs.promises.access(targetPath)
        return { exists: true }
      } catch (error: any) {
        return { exists: false, error: error.message }
      }
    })

    ipcMain.handle('vault:readFile', async (event: IpcMainInvokeEvent, filePath: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized' }
      if (!this.validateInput(filePath, 'string', 500)) return { success: false, error: 'Invalid file path' }

      try {
        const fs = await import('fs')
        const content = await fs.promises.readFile(filePath, 'utf8')
        return { success: true, content }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    })

    // High-level vault operations
    ipcMain.handle('vault:getVaultRegistry', async (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null

      try {
        const fs = await import('fs')
        const path = await import('path')
        const os = await import('os')

        // First try to get saved vault root path from database
        let vaultRoot = this.db.getSetting('vault-root-path')
        console.log('[ELECTRON] vault:getVaultRegistry - Saved vault root from DB:', vaultRoot)

        // Fall back to default if no saved path
        if (!vaultRoot) {
          vaultRoot = path.join(os.homedir(), 'Documents', 'ChatLo_Vaults')
          console.log('[ELECTRON] vault:getVaultRegistry - Using default vault root:', vaultRoot)
        }

        const registryPath = path.join(vaultRoot, '.chatlo', 'vault-registry.json')
        console.log('[ELECTRON] vault:getVaultRegistry - Looking for registry at:', registryPath)

        if (!await fs.promises.access(registryPath).then(() => true).catch(() => false)) {
          console.log('[ELECTRON] vault:getVaultRegistry - Registry file not found')
          return null
        }

        const content = await fs.promises.readFile(registryPath, 'utf8')
        const registry = JSON.parse(content)
        console.log('[ELECTRON] vault:getVaultRegistry - Registry loaded successfully:', registry)
        return registry
      } catch (error: any) {
        console.error('[ELECTRON] Error reading vault registry:', error)
        return null
      }
    })

    ipcMain.handle('vault:saveVaultRegistry', async (event: IpcMainInvokeEvent, registry: any) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized' }
      if (!registry || typeof registry !== 'object') return { success: false, error: 'Invalid registry object' }

      try {
        const fs = await import('fs')
        const path = await import('path')

        const registryPath = path.join(registry.vaultRoot, '.chatlo', 'vault-registry.json')
        const registryDir = path.dirname(registryPath)

        // Ensure directory exists
        await fs.promises.mkdir(registryDir, { recursive: true })

        // Save registry
        await fs.promises.writeFile(registryPath, JSON.stringify(registry, null, 2), 'utf8')
        return { success: true }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    })

    ipcMain.handle('vault:initializeVaultRoot', async (event: IpcMainInvokeEvent, rootPath: string, template: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized', vaults: [] }
      if (!this.validateInput(rootPath, 'string', 500)) return { success: false, error: 'Invalid root path', vaults: [] }
      if (!this.validateInput(template, 'string', 50)) return { success: false, error: 'Invalid template', vaults: [] }

      try {
        const fs = await import('fs')
        const path = await import('path')

        // Create root directory if it doesn't exist
        await fs.promises.mkdir(rootPath, { recursive: true })

        // Create basic vault structure based on template
        const vaults: any[] = []

        if (template === 'default') {
          // Create Personal and Work vaults
          const personalPath = path.join(rootPath, 'personal-vault')
          const workPath = path.join(rootPath, 'work-vault')

          await fs.promises.mkdir(personalPath, { recursive: true })
          await fs.promises.mkdir(workPath, { recursive: true })

          // Create getting-started context in personal vault
          const gettingStartedPath = path.join(personalPath, 'getting-started')
          await this.createContextStructure(gettingStartedPath, 'Your First Context Vault')

          // Create projects context in work vault
          const projectsPath = path.join(workPath, 'projects')
          await this.createContextStructure(projectsPath, 'Projects')

          vaults.push(
            { id: 'personal-vault', name: 'Personal Vault', path: personalPath },
            { id: 'work-vault', name: 'Work Vault', path: workPath }
          )
        } else if (template === 'simple') {
          // Create single vault
          const vaultPath = path.join(rootPath, 'my-vault')
          await fs.promises.mkdir(vaultPath, { recursive: true })

          // Create getting-started context
          const gettingStartedPath = path.join(vaultPath, 'getting-started')
          await this.createContextStructure(gettingStartedPath, 'Getting Started')

          vaults.push({ id: 'my-vault', name: 'My Vault', path: vaultPath })
        }

        return { success: true, vaults }
      } catch (error: any) {
        return { success: false, error: error.message, vaults: [] }
      }
    })

    ipcMain.handle('vault:scanContexts', async (event: IpcMainInvokeEvent, vaultPath: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized', contexts: [] }
      if (!this.validateInput(vaultPath, 'string', 500)) return { success: false, error: 'Invalid vault path', contexts: [] }

      try {
        const fs = await import('fs')
        const path = await import('path')

        const items = await fs.promises.readdir(vaultPath, { withFileTypes: true })
        const contexts: any[] = []

        for (const item of items) {
          if (item.isDirectory() && !item.name.startsWith('.')) {
            const contextPath = path.join(vaultPath, item.name)
            const masterPath = path.join(contextPath, 'master.md')

            // Check if master.md exists
            const masterExists = await fs.promises.access(masterPath).then(() => true).catch(() => false)

            if (masterExists) {
              contexts.push({
                id: item.name,
                name: item.name,
                path: contextPath,
                hasMaster: true
              })
            }
          }
        }

        return { success: true, contexts }
      } catch (error: any) {
        return { success: false, error: error.message, contexts: [] }
      }
    })

    // Vault file operations
    ipcMain.handle('vault:copyFilesToContext', async (event: IpcMainInvokeEvent, files: { path: string; name: string }[], contextPath: string, subfolder?: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized' }
      if (!Array.isArray(files)) return { success: false, error: 'Invalid files array' }
      if (!this.validateInput(contextPath, 'string', 500)) return { success: false, error: 'Invalid context path' }

      try {
        const fs = await import('fs')
        const path = await import('path')
        const copiedFiles: string[] = []

        // Determine target directory
        const targetDir = subfolder
          ? path.join(contextPath, subfolder)
          : path.join(contextPath, 'documents')

        // Ensure target directory exists
        await fs.promises.mkdir(targetDir, { recursive: true })

        for (const file of files) {
          if (!file.path || !file.name) continue

          // Generate unique filename if file already exists
          let targetPath = path.join(targetDir, file.name)
          let counter = 1
          const ext = path.extname(file.name)
          const baseName = path.basename(file.name, ext)

          while (await fs.promises.access(targetPath).then(() => true).catch(() => false)) {
            targetPath = path.join(targetDir, `${baseName}_${counter}${ext}`)
            counter++
          }

          // Copy the file
          await fs.promises.copyFile(file.path, targetPath)
          copiedFiles.push(targetPath)
          console.log(`[ELECTRON] Copied file to vault: ${file.name} -> ${targetPath}`)
        }

        return { success: true, copiedFiles }
      } catch (error: any) {
        console.error('[ELECTRON] Error copying files to context:', error)
        return { success: false, error: error.message }
      }
    })

    ipcMain.handle('vault:linkChatToContext', async (event: IpcMainInvokeEvent, conversationId: string, contextId: string, contextPath: string) => {
      if (!this.validateSender(event.senderFrame)) return { success: false, error: 'Unauthorized' }
      if (!this.validateInput(conversationId, 'string', 100)) return { success: false, error: 'Invalid conversation ID' }
      if (!this.validateInput(contextId, 'string', 100)) return { success: false, error: 'Invalid context ID' }
      if (!this.validateInput(contextPath, 'string', 500)) return { success: false, error: 'Invalid context path' }

      try {
        const fs = await import('fs')
        const path = await import('path')

        // Update chat links file in context
        const chatLinksPath = path.join(contextPath, '.context', 'chat-links.json')

        let chatLinks: { conversations: any[] } = { conversations: [] }
        if (await fs.promises.access(chatLinksPath).then(() => true).catch(() => false)) {
          const content = await fs.promises.readFile(chatLinksPath, 'utf8')
          chatLinks = JSON.parse(content)
        }

        // Check if conversation is already linked
        const existingLink = chatLinks.conversations.find((c: any) => c.id === conversationId)
        if (!existingLink) {
          // Get conversation details from database
          const conversation = this.db.getConversation(conversationId)
          if (conversation) {
            chatLinks.conversations.push({
              id: conversationId,
              title: conversation.title,
              linkedAt: new Date().toISOString(),
              contextId
            })

            // Save updated chat links
            await fs.promises.writeFile(chatLinksPath, JSON.stringify(chatLinks, null, 2), 'utf8')
            console.log(`[ELECTRON] Linked conversation ${conversationId} to context ${contextId}`)
          }
        }

        // Store the context link in the database for quick access
        this.db.setSetting(`conversation-context-${conversationId}`, contextId)

        return { success: true }
      } catch (error: any) {
        console.error('[ELECTRON] Error linking chat to context:', error)
        return { success: false, error: error.message }
      }
    })

    ipcMain.handle('vault:getChatContext', async (event: IpcMainInvokeEvent, conversationId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(conversationId, 'string', 100)) return null

      try {
        // Get context link from database
        const contextId = this.db.getSetting(`conversation-context-${conversationId}`)
        return contextId
      } catch (error: any) {
        console.error('[ELECTRON] Error getting chat context:', error)
        return null
      }
    })

    // Artifact IPC handlers
    ipcMain.handle('db:getArtifacts', (event: IpcMainInvokeEvent, messageId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      return this.db.getArtifacts(messageId)
    })

    ipcMain.handle('db:addArtifact', (event: IpcMainInvokeEvent, messageId: string, artifact: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
      if (!artifact || typeof artifact !== 'object') throw new Error('Invalid artifact object')
      return this.db.addArtifact(messageId, artifact)
    })

    // Database diagnostics
    ipcMain.handle('db:getDatabaseHealth', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return this.db.getDatabaseHealth()
    })

    ipcMain.handle('db:createBackup', (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null
      return this.db.createBackup()
    })

    ipcMain.handle('db:updateArtifact', (event: IpcMainInvokeEvent, id: string, updates: any) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid artifact ID')
      if (!updates || typeof updates !== 'object') throw new Error('Invalid updates object')
      return this.db.updateArtifact(id, updates)
    })

    ipcMain.handle('db:removeArtifact', (event: IpcMainInvokeEvent, id: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid artifact ID')
      return this.db.removeArtifact(id)
    })

    ipcMain.handle('db:getConversationArtifacts', (event: IpcMainInvokeEvent, conversationId: string) => {
      if (!this.validateSender(event.senderFrame)) return null
      if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
      return this.db.getConversationArtifacts(conversationId)
    })

    // Auto-updater IPC handlers
    ipcMain.handle('updater:check-for-updates', async (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null

      if (isDev) {
        return { available: false, message: 'Updates not available in development mode' }
      }

      try {
        const result = await autoUpdater.checkForUpdates()
        return { available: result ? result.updateInfo.version !== app.getVersion() : false }
      } catch (error: any) {
        console.error('Error checking for updates:', error)
        return { available: false, error: error.message }
      }
    })

    ipcMain.handle('updater:download-and-install', async (event: IpcMainInvokeEvent) => {
      if (!this.validateSender(event.senderFrame)) return null

      if (isDev) {
        return { success: false, message: 'Updates not available in development mode' }
      }

      try {
        await autoUpdater.downloadUpdate()
        autoUpdater.quitAndInstall()
        return { success: true }
      } catch (error: any) {
        console.error('Error downloading/installing update:', error)
        return { success: false, error: error.message }
      }
    })
  }

  private setupAutoUpdater(): void {
    if (!isDev) {
      autoUpdater.checkForUpdatesAndNotify()
    }

    // Auto-updater events
    autoUpdater.on('checking-for-update', () => {
      console.log('Checking for update...')
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:checking-for-update')
      }
    })

    autoUpdater.on('update-available', (info) => {
      console.log('Update available:', info.version)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-available', info)
      }
    })

    autoUpdater.on('update-not-available', (info) => {
      console.log('Update not available')
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-not-available')
      }
    })

    autoUpdater.on('error', (err) => {
      console.error('Auto-updater error:', err)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:error', err.message)
      }
    })

    autoUpdater.on('download-progress', (progressObj) => {
      console.log(`Download progress: ${progressObj.percent}%`)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:download-progress', progressObj)
      }
    })

    autoUpdater.on('update-downloaded', (info) => {
      console.log('Update downloaded:', info.version)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-downloaded', info)
      }
    })
  }

  public async init(): Promise<void> {
    await app.whenReady()

    // Initialize file system only if no vault system is configured
    try {
      // Check if vault system is configured
      const vaultRootPath = this.db.getSetting('vault-root-path')
      console.log('[ELECTRON] Checking for vault system, vault root path:', vaultRootPath)

      if (vaultRootPath) {
        console.log('[ELECTRON] Vault system detected, skipping legacy file system initialization')
      } else {
        console.log('[ELECTRON] No vault system found, initializing legacy file system')
        await this.fileSystem.initializeChatloFolder()
        await this.fileSystem.indexAllFiles()
        console.log('Legacy file system initialized successfully')
      }
    } catch (error) {
      console.error('Error initializing file system:', error)
    }

    this.setupIPC()
    this.setupAutoUpdater()
    this.createWindow()

    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow()
      }
    })

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit()
      }
    })
  }
}

const application = new App()
application.init().catch(console.error)

ipcMain.on('window-minimize', () => {
  if (application.mainWindow) application.mainWindow.minimize();
});
ipcMain.on('window-maximize', () => {
  if (application.mainWindow) {
    if (application.mainWindow.isMaximized()) {
      application.mainWindow.unmaximize();
    } else {
      application.mainWindow.maximize();
    }
  }
});
ipcMain.on('window-close', () => {
  if (application.mainWindow) application.mainWindow.close();
});
