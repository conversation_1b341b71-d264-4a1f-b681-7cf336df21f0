---
type: "manual"
---

Based on the FontAwesome issues we just fixed, here's a rule you can add to my settings to help prevent similar problems in the future:

## FontAwesome Icon Usage Rule

**Rule:** When working with FontAwesome icons in React/TypeScript projects, always:

1. **Use the centralized icon system** - Import icons from the centralized `FontAwesomeIcons.tsx` file using `Icons.iconName` rather than importing individual icons directly from `@fortawesome/free-solid-svg-icons`

2. **Verify icon names exist** - Before using any FontAwesome icon, check that it exists in the free solid icons package. Common naming issues:
   - `faEllipsisHorizontal` → `faEllipsis`
   - `faExternalLinkAlt` → `faExternalLink`
   - `faMapMarker` → `faMapMarkerAlt`
   - `faRandom` → `faShuffle`
   - `faMagic` → `faWandMagicSparkles`
   - `faPin` → `faThumbtack`
   - `faFileText` → `faFileAlt`
   - `faUserRobot` (doesn't exist in free version)

3. **Import pattern** - Always import from centralized system:
   ```typescript
   import { Icon, Icons, FontAwesomeIcon } from './icons/FontAwesomeIcons'
   
   // Use Icons object
   icon: Icons.folderOpen
   
   // Or use Icon component
   <Icon name="folderOpen" size="md" />
   ```

4. **Avoid direct imports** - Don't import icons directly like:
   ```typescript
   // ❌ Don't do this
   import { faFolderOpen } from '@fortawesome/free-solid-svg-icons'
   
   // ✅ Do this instead
   import { Icons } from './icons/FontAwesomeIcons'
   ```

This rule will help prevent FontAwesome import errors and maintain consistency across the codebase by using the centralized icon system.
