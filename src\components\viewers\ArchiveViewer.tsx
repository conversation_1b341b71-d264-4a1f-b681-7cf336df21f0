import React, { useState, useEffect } from 'react'
import { Icon } from '../icons/FontAwesomeIcons'
import { FileTreeNode } from '../../types'

interface ArchiveEntry {
  name: string
  path: string
  type: 'file' | 'folder'
  size: number
  compressedSize: number
  modified: string
  ratio: number
  children?: ArchiveEntry[]
  isExpanded?: boolean
}

interface ArchiveViewerProps {
  file: FileTreeNode
  className?: string
}

type SortField = 'name' | 'size' | 'modified' | 'ratio'
type SortDirection = 'asc' | 'desc'
type ViewMode = 'list' | 'tree'

const ArchiveViewer: React.FC<ArchiveViewerProps> = ({
  file,
  className = ''
}) => {
  const [entries, setEntries] = useState<ArchiveEntry[]>([])
  const [filteredEntries, setFilteredEntries] = useState<ArchiveEntry[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [sortField, setSortField] = useState<SortField>('name')
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')
  const [viewMode, setViewMode] = useState<ViewMode>('tree')
  const [selectedEntry, setSelectedEntry] = useState<ArchiveEntry | null>(null)
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set())

  useEffect(() => {
    loadArchiveContents()
  }, [file])

  useEffect(() => {
    filterAndSortEntries()
  }, [entries, searchTerm, sortField, sortDirection])

  const loadArchiveContents = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      // Simulate loading archive contents
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockEntries: ArchiveEntry[] = [
        {
          name: 'README.md',
          path: 'README.md',
          type: 'file',
          size: 2048,
          compressedSize: 1024,
          modified: '2024-01-15T10:30:00Z',
          ratio: 50
        },
        {
          name: 'src',
          path: 'src/',
          type: 'folder',
          size: 0,
          compressedSize: 0,
          modified: '2024-01-15T10:30:00Z',
          ratio: 0,
          children: [
            {
              name: 'index.js',
              path: 'src/index.js',
              type: 'file',
              size: 4096,
              compressedSize: 2048,
              modified: '2024-01-15T10:25:00Z',
              ratio: 50
            },
            {
              name: 'components',
              path: 'src/components/',
              type: 'folder',
              size: 0,
              compressedSize: 0,
              modified: '2024-01-15T10:20:00Z',
              ratio: 0,
              children: [
                {
                  name: 'App.jsx',
                  path: 'src/components/App.jsx',
                  type: 'file',
                  size: 8192,
                  compressedSize: 3072,
                  modified: '2024-01-15T10:15:00Z',
                  ratio: 62.5
                },
                {
                  name: 'Button.jsx',
                  path: 'src/components/Button.jsx',
                  type: 'file',
                  size: 1536,
                  compressedSize: 768,
                  modified: '2024-01-15T10:10:00Z',
                  ratio: 50
                }
              ]
            },
            {
              name: 'utils.js',
              path: 'src/utils.js',
              type: 'file',
              size: 2048,
              compressedSize: 1024,
              modified: '2024-01-15T10:05:00Z',
              ratio: 50
            }
          ]
        },
        {
          name: 'package.json',
          path: 'package.json',
          type: 'file',
          size: 1024,
          compressedSize: 512,
          modified: '2024-01-15T10:00:00Z',
          ratio: 50
        },
        {
          name: 'assets',
          path: 'assets/',
          type: 'folder',
          size: 0,
          compressedSize: 0,
          modified: '2024-01-15T09:55:00Z',
          ratio: 0,
          children: [
            {
              name: 'logo.png',
              path: 'assets/logo.png',
              type: 'file',
              size: 16384,
              compressedSize: 14336,
              modified: '2024-01-15T09:50:00Z',
              ratio: 12.5
            },
            {
              name: 'styles.css',
              path: 'assets/styles.css',
              type: 'file',
              size: 4096,
              compressedSize: 2048,
              modified: '2024-01-15T09:45:00Z',
              ratio: 50
            }
          ]
        }
      ]
      
      setEntries(mockEntries)
      setExpandedFolders(new Set(['src/', 'src/components/', 'assets/']))
    } catch (err) {
      setError('Failed to load archive contents')
    } finally {
      setIsLoading(false)
    }
  }

  const filterAndSortEntries = () => {
    let filtered = entries
    
    if (searchTerm) {
      filtered = filterEntriesRecursive(entries, searchTerm.toLowerCase())
    }
    
    const sorted = sortEntriesRecursive(filtered, sortField, sortDirection)
    setFilteredEntries(sorted)
  }

  const filterEntriesRecursive = (entries: ArchiveEntry[], term: string): ArchiveEntry[] => {
    return entries.filter(entry => {
      const nameMatch = entry.name.toLowerCase().includes(term)
      const hasMatchingChildren = entry.children && filterEntriesRecursive(entry.children, term).length > 0
      
      if (nameMatch || hasMatchingChildren) {
        return {
          ...entry,
          children: entry.children ? filterEntriesRecursive(entry.children, term) : undefined
        }
      }
      
      return false
    }).map(entry => ({
      ...entry,
      children: entry.children ? filterEntriesRecursive(entry.children, term) : undefined
    }))
  }

  const sortEntriesRecursive = (entries: ArchiveEntry[], field: SortField, direction: SortDirection): ArchiveEntry[] => {
    const sorted = [...entries].sort((a, b) => {
      // Folders first
      if (a.type !== b.type) {
        return a.type === 'folder' ? -1 : 1
      }
      
      let comparison = 0
      switch (field) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'size':
          comparison = a.size - b.size
          break
        case 'modified':
          comparison = new Date(a.modified).getTime() - new Date(b.modified).getTime()
          break
        case 'ratio':
          comparison = a.ratio - b.ratio
          break
      }
      
      return direction === 'asc' ? comparison : -comparison
    })
    
    return sorted.map(entry => ({
      ...entry,
      children: entry.children ? sortEntriesRecursive(entry.children, field, direction) : undefined
    }))
  }

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const toggleFolder = (path: string) => {
    const newExpanded = new Set(expandedFolders)
    if (newExpanded.has(path)) {
      newExpanded.delete(path)
    } else {
      newExpanded.add(path)
    }
    setExpandedFolders(newExpanded)
  }

  const getFileIcon = (entry: ArchiveEntry) => {
    if (entry.type === 'folder') {
      return expandedFolders.has(entry.path) ? faFolderOpen : faFolder
    }
    
    const extension = entry.name.split('.').pop()?.toLowerCase() || ''
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(extension)) return faFileImage
    if (['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'html', 'css'].includes(extension)) return faFileCode
    if (['txt', 'md', 'log', 'json', 'xml', 'csv', 'yaml', 'yml'].includes(extension)) return faFileText
    if (extension === 'pdf') return faFilePdf
    if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].includes(extension)) return faFileArchive
    
    return faFile
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getTotalStats = () => {
    const calculateStats = (entries: ArchiveEntry[]): { files: number; folders: number; totalSize: number; compressedSize: number } => {
      let files = 0, folders = 0, totalSize = 0, compressedSize = 0
      
      entries.forEach(entry => {
        if (entry.type === 'file') {
          files++
          totalSize += entry.size
          compressedSize += entry.compressedSize
        } else {
          folders++
          if (entry.children) {
            const childStats = calculateStats(entry.children)
            files += childStats.files
            folders += childStats.folders
            totalSize += childStats.totalSize
            compressedSize += childStats.compressedSize
          }
        }
      })
      
      return { files, folders, totalSize, compressedSize }
    }
    
    return calculateStats(entries)
  }

  const renderTreeEntry = (entry: ArchiveEntry, level: number = 0) => {
    const isExpanded = expandedFolders.has(entry.path)
    const isSelected = selectedEntry?.path === entry.path
    
    return (
      <div key={entry.path}>
        <div
          className={`
            flex items-center gap-2 px-3 py-1.5 hover:bg-neutral-700/50 cursor-pointer transition-colors
            ${isSelected ? 'bg-primary/20 border-l-2 border-primary' : ''}
          `}
          style={{ paddingLeft: `${12 + level * 20}px` }}
          onClick={() => {
            setSelectedEntry(entry)
            if (entry.type === 'folder') {
              toggleFolder(entry.path)
            }
          }}
        >
          {entry.type === 'folder' && (
            <FontAwesomeIcon 
              icon={isExpanded ? faChevronDown : faChevronRight} 
              className="w-3 h-3 text-neutral-500" 
            />
          )}
          <FontAwesomeIcon 
            icon={getFileIcon(entry)} 
            className={`w-4 h-4 ${entry.type === 'folder' ? 'text-primary' : 'text-neutral-400'}`} 
          />
          <span className="flex-1 text-neutral-300 truncate">{entry.name}</span>
          {entry.type === 'file' && (
            <span className="text-xs text-neutral-500">{formatFileSize(entry.size)}</span>
          )}
        </div>
        
        {entry.type === 'folder' && isExpanded && entry.children && (
          <div>
            {entry.children.map(child => renderTreeEntry(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  const renderListEntry = (entry: ArchiveEntry) => {
    const isSelected = selectedEntry?.path === entry.path
    
    return (
      <div
        key={entry.path}
        className={`
          flex items-center gap-3 px-3 py-2 hover:bg-neutral-700/50 cursor-pointer transition-colors border-b border-neutral-800
          ${isSelected ? 'bg-primary/20 border-l-2 border-primary' : ''}
        `}
        onClick={() => setSelectedEntry(entry)}
      >
        <FontAwesomeIcon 
          icon={getFileIcon(entry)} 
          className={`w-4 h-4 ${entry.type === 'folder' ? 'text-primary' : 'text-neutral-400'}`} 
        />
        <div className="flex-1 min-w-0">
          <div className="text-neutral-300 truncate">{entry.name}</div>
          <div className="text-xs text-neutral-500 truncate">{entry.path}</div>
        </div>
        <div className="text-sm text-neutral-400 text-right min-w-20">
          {entry.type === 'file' ? formatFileSize(entry.size) : '—'}
        </div>
        <div className="text-sm text-neutral-400 text-right min-w-24">
          {entry.type === 'file' ? `${entry.ratio.toFixed(1)}%` : '—'}
        </div>
        <div className="text-sm text-neutral-400 text-right min-w-32">
          {formatDate(entry.modified)}
        </div>
      </div>
    )
  }

  const stats = getTotalStats()

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center bg-neutral-900 ${className}`}>
        <div className="text-center">
          <FontAwesomeIcon icon={faSpinner} className="w-8 h-8 text-primary animate-spin mb-4" />
          <div className="text-neutral-300">Loading archive contents...</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center bg-neutral-900 ${className}`}>
        <div className="text-center">
          <FontAwesomeIcon icon={faExclamationTriangle} className="w-8 h-8 text-red-400 mb-4" />
          <div className="text-red-300 mb-2">Failed to load archive</div>
          <div className="text-neutral-400 text-sm">{error}</div>
        </div>
      </div>
    )
  }

  return (
    <div className={`flex flex-col bg-neutral-900 ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-3 border-b border-neutral-700 bg-neutral-800/50">
        <div className="flex items-center gap-3">
          {/* Search */}
          <div className="relative">
            <FontAwesomeIcon 
              icon={faSearch} 
              className="absolute left-3 top-1/2 transform -translate-y-1/2 w-3 h-3 text-neutral-500" 
            />
            <input
              type="text"
              placeholder="Search files..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9 pr-3 py-1.5 bg-neutral-800 border border-neutral-600 rounded-lg text-sm text-neutral-300 placeholder-neutral-500 focus:outline-none focus:border-primary"
            />
          </div>

          {/* View mode toggle */}
          <div className="flex bg-neutral-800 rounded-lg p-1">
            <button
              onClick={() => setViewMode('tree')}
              className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                viewMode === 'tree'
                  ? 'bg-neutral-700 text-white'
                  : 'text-neutral-400 hover:text-white'
              }`}
            >
              <FontAwesomeIcon icon={faList} className="w-3 h-3 mr-2" />
              Tree
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                viewMode === 'list'
                  ? 'bg-neutral-700 text-white'
                  : 'text-neutral-400 hover:text-white'
              }`}
            >
              <FontAwesomeIcon icon={faTh} className="w-3 h-3 mr-2" />
              List
            </button>
          </div>
        </div>

        <div className="flex items-center gap-3">
          {/* Archive stats */}
          <div className="text-sm text-neutral-400">
            {stats.files} files • {stats.folders} folders • {formatFileSize(stats.totalSize)}
          </div>

          {/* Action buttons */}
          <div className="flex items-center gap-2">
            <button
              className="px-3 py-1.5 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors text-sm flex items-center gap-2"
              title="Extract archive"
            >
              <FontAwesomeIcon icon={faDownload} className="w-3 h-3" />
              Extract
            </button>
            <button
              className="p-1.5 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded transition-colors"
              title="Download archive"
            >
              <FontAwesomeIcon icon={faDownload} className="w-3 h-3" />
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto">
        {viewMode === 'tree' ? (
          <div>
            {filteredEntries.map(entry => renderTreeEntry(entry))}
          </div>
        ) : (
          <div>
            {/* List header */}
            <div className="flex items-center gap-3 px-3 py-2 bg-neutral-800/50 border-b border-neutral-700 text-sm text-neutral-400">
              <div className="w-4"></div>
              <button
                onClick={() => handleSort('name')}
                className="flex-1 text-left flex items-center gap-1 hover:text-white transition-colors"
              >
                Name
                {sortField === 'name' && (
                  <FontAwesomeIcon icon={sortDirection === 'asc' ? faSortUp : faSortDown} className="w-3 h-3" />
                )}
              </button>
              <button
                onClick={() => handleSort('size')}
                className="min-w-20 text-right flex items-center justify-end gap-1 hover:text-white transition-colors"
              >
                Size
                {sortField === 'size' && (
                  <FontAwesomeIcon icon={sortDirection === 'asc' ? faSortUp : faSortDown} className="w-3 h-3" />
                )}
              </button>
              <button
                onClick={() => handleSort('ratio')}
                className="min-w-24 text-right flex items-center justify-end gap-1 hover:text-white transition-colors"
              >
                Compression
                {sortField === 'ratio' && (
                  <FontAwesomeIcon icon={sortDirection === 'asc' ? faSortUp : faSortDown} className="w-3 h-3" />
                )}
              </button>
              <button
                onClick={() => handleSort('modified')}
                className="min-w-32 text-right flex items-center justify-end gap-1 hover:text-white transition-colors"
              >
                Modified
                {sortField === 'modified' && (
                  <FontAwesomeIcon icon={sortDirection === 'asc' ? faSortUp : faSortDown} className="w-3 h-3" />
                )}
              </button>
            </div>
            
            {/* List entries */}
            {filteredEntries.map(entry => renderListEntry(entry))}
          </div>
        )}
      </div>

      {/* Status bar */}
      <div className="flex items-center justify-between px-3 py-2 border-t border-neutral-700 bg-neutral-800/30 text-xs text-neutral-500">
        <div className="flex items-center gap-4">
          <span className="flex items-center gap-1">
            <FontAwesomeIcon icon={faFileArchive} className="w-3 h-3" />
            Archive
          </span>
          <span className="flex items-center gap-1">
            <FontAwesomeIcon icon={faWeight} className="w-3 h-3" />
            {formatFileSize(stats.compressedSize)} / {formatFileSize(stats.totalSize)}
          </span>
          <span className="flex items-center gap-1">
            <FontAwesomeIcon icon={faCompress} className="w-3 h-3" />
            {((1 - stats.compressedSize / stats.totalSize) * 100).toFixed(1)}% compression
          </span>
        </div>
        
        <div className="flex items-center gap-4">
          <span>{filteredEntries.length} items shown</span>
          <span className="capitalize">View: {viewMode}</span>
        </div>
      </div>
    </div>
  )
}

export default ArchiveViewer
