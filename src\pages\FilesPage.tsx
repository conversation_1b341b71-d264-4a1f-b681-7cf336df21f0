import React, { useState, useEffect } from 'react'
import { useParams, useSearchParams } from 'react-router-dom'
import { ArtifactsSidebar } from '../components/artifacts/ArtifactsSidebar'
import { useAppStore } from '../store'
import { FileTreeNode } from '../types'
import { vaultUIManager } from '../services/vaultUIManager'

import FileContextMenu from '../components/FileContextMenu'
import FileActionsToolbar from '../components/FileActionsToolbar'
import FilePreviewPanel from '../components/FilePreviewPanel'
import FileTypeIcon from '../components/FileTypeIcon'
import FileViewerModal from '../components/viewers/FileViewerModal'
import { Icon, Icons, FontAwesomeIcon } from '../components/icons/FontAwesomeIcons'

// FileTreeNode interface is now imported from types

interface ViewModeState {
  currentMode: 'explorer' | 'master'
  showArtifacts: boolean
  artifactsExpanded: boolean
}

// Helper function to get file type icon and color
const getFileTypeIcon = (fileName: string, fileType: string) => {
  const extension = fileName.split('.').pop()?.toLowerCase()

  if (fileType === 'Folder') {
    return { icon: Icons.folder, color: 'text-supplement2', bgColor: 'bg-supplement2/20' }
  }

  switch (extension) {
    case 'md':
    case 'markdown':
      return { icon: Icons.fileText, color: 'text-primary', bgColor: 'bg-primary/20' }
    case 'json':
      return { icon: Icons.fileCode, color: 'text-yellow-400', bgColor: 'bg-yellow-500/20' }
    case 'js':
    case 'jsx':
    case 'ts':
    case 'tsx':
      return { icon: Icons.fileCode, color: 'text-blue-400', bgColor: 'bg-blue-500/20' }
    case 'pdf':
      return { icon: Icons.filePdf, color: 'text-red-400', bgColor: 'bg-red-500/20' }
    case 'doc':
    case 'docx':
      return { icon: Icons.fileWord, color: 'text-blue-600', bgColor: 'bg-blue-600/20' }
    case 'xls':
    case 'xlsx':
      return { icon: Icons.fileExcel, color: 'text-green-400', bgColor: 'bg-green-500/20' }
    case 'ppt':
    case 'pptx':
      return { icon: Icons.filePowerpoint, color: 'text-orange-400', bgColor: 'bg-orange-500/20' }
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'svg':
    case 'webp':
      return { icon: Icons.fileImage, color: 'text-purple-400', bgColor: 'bg-purple-500/20' }
    case 'mp4':
    case 'avi':
    case 'mov':
    case 'wmv':
      return { icon: Icons.fileVideo, color: 'text-pink-400', bgColor: 'bg-pink-500/20' }
    case 'mp3':
    case 'wav':
    case 'flac':
      return { icon: Icons.fileAudio, color: 'text-indigo-400', bgColor: 'bg-indigo-500/20' }
    case 'zip':
    case 'rar':
    case '7z':
      return { icon: Icons.fileArchive, color: 'text-gray-400', bgColor: 'bg-gray-500/20' }
    case 'css':
      return { icon: Icons.fileCode, color: 'text-cyan-400', bgColor: 'bg-cyan-500/20' }
    case 'html':
    case 'htm':
      return { icon: Icons.fileCode, color: 'text-orange-400', bgColor: 'bg-orange-500/20' }
    default:
      return { icon: Icons.file, color: 'text-supplement1', bgColor: 'bg-supplement1/20' }
  }
}

const FilesPage: React.FC = () => {
  const { contextId } = useParams()
  const [searchParams] = useSearchParams()
  const { artifactsVisible } = useAppStore()

  // Get context from URL params
  const contextFromUrl = searchParams.get('context') || contextId

  // State for vault data
  const [fileTree, setFileTree] = useState<FileTreeNode[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [dragOver, setDragOver] = useState<string | null>(null)

  // State for UI
  const [viewMode, setViewMode] = useState<ViewModeState>({
    currentMode: 'explorer', // Start with explorer, will switch to master when master.md is found
    showArtifacts: false,
    artifactsExpanded: false
  })

  const [selectedFile, setSelectedFile] = useState<string | null>(null)
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set())
  const [masterContent, setMasterContent] = useState<string>('')
  const [masterLoading, setMasterLoading] = useState(false)

  // New UI state for file opening features
  const [selectedFiles, setSelectedFiles] = useState<FileTreeNode[]>([])
  const [contextMenu, setContextMenu] = useState<{
    file: FileTreeNode
    position: { x: number; y: number }
  } | null>(null)
  const [fileViewerModal, setFileViewerModal] = useState<{
    file: FileTreeNode
    viewMode: 'preview' | 'edit'
  } | null>(null)
  const [toast, setToast] = useState<{
    message: string
    type: 'success' | 'error' | 'warning' | 'info'
  } | null>(null)

  // NEW: Windows Explorer-style state
  const [showPreview, setShowPreview] = useState<boolean>(() => {
    const saved = localStorage.getItem('fileExplorer.showPreview')
    return saved ? JSON.parse(saved) : true
  })
  const [currentFolder, setCurrentFolder] = useState<string | null>(null)
  const [viewType, setViewType] = useState<'list' | 'grid'>('list')
  const [sortBy, setSortBy] = useState<'name' | 'size' | 'date' | 'type'>('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [breadcrumbs, setBreadcrumbs] = useState<Array<{name: string, path: string}>>([])

  // Save preview preference
  useEffect(() => {
    localStorage.setItem('fileExplorer.showPreview', JSON.stringify(showPreview))
  }, [showPreview])

  // Helper functions for Windows Explorer functionality
  const togglePreview = () => {
    setShowPreview(!showPreview)
  }

  const getCurrentFolderContents = (): FileTreeNode[] => {
    if (!currentFolder) {
      return fileTree // Show root level
    }

    const findFolder = (nodes: FileTreeNode[], path: string): FileTreeNode | null => {
      for (const node of nodes) {
        if (node.path === path) return node
        if (node.children) {
          const found = findFolder(node.children, path)
          if (found) return found
        }
      }
      return null
    }

    const folder = findFolder(fileTree, currentFolder)
    return folder?.children || []
  }

  const sortFiles = (files: FileTreeNode[]): FileTreeNode[] => {
    const sorted = [...files].sort((a, b) => {
      let comparison = 0

      // Always put folders first
      if (a.type === 'folder' && b.type === 'file') return -1
      if (a.type === 'file' && b.type === 'folder') return 1

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'size':
          comparison = (a.size || 0) - (b.size || 0)
          break
        case 'date':
          comparison = new Date(a.modified || 0).getTime() - new Date(b.modified || 0).getTime()
          break
        case 'type':
          const aExt = a.name.split('.').pop() || ''
          const bExt = b.name.split('.').pop() || ''
          comparison = aExt.localeCompare(bExt)
          break
      }

      return sortOrder === 'asc' ? comparison : -comparison
    })

    return sorted
  }

  const navigateToFolder = (folderPath: string | null) => {
    setCurrentFolder(folderPath)
    setSelectedFile(null)

    // Update breadcrumbs
    if (!folderPath) {
      setBreadcrumbs([])
    } else {
      const parts = folderPath.split('/')
      const crumbs = parts.map((part, index) => ({
        name: part,
        path: parts.slice(0, index + 1).join('/')
      }))
      setBreadcrumbs(crumbs)
    }
  }

  // Load vault file tree on mount
  useEffect(() => {
    console.log('FilesPage mounted, loading file tree...')
    loadFileTree()
  }, []) // Run once on mount

  // Reload when context changes
  useEffect(() => {
    if (contextFromUrl) {
      console.log('Context changed to:', contextFromUrl, 'reloading file tree...')
      loadFileTree()
    }
  }, [contextFromUrl])

  // Debug: Log when fileTree changes
  useEffect(() => {
    console.log('File tree updated:', fileTree.length, 'items')
    console.log('Expanded folders:', Array.from(expandedFolders))
    console.log('Selected file:', selectedFile)
    console.log('Current view mode:', viewMode.currentMode)
  }, [fileTree, expandedFolders, selectedFile, viewMode])

  const loadFileTree = async () => {
    try {
      setLoading(true)
      setError(null)

      console.log('🔍 DEBUG: Starting file tree load...')
      console.log('🔍 DEBUG: contextFromUrl:', contextFromUrl)
      console.log('🔍 DEBUG: window.electronAPI available:', !!window.electronAPI)
      console.log('🔍 DEBUG: window.electronAPI.vault available:', !!window.electronAPI?.vault)

      const tree = await vaultUIManager.getFileTree(contextFromUrl || undefined)
      console.log('🔍 DEBUG: Raw file tree from vaultUIManager:', tree)
      console.log('🔍 DEBUG: File tree length:', tree.length)

      if (tree.length > 0) {
        console.log('🔍 DEBUG: First file tree item:', tree[0])
      }

      setFileTree(tree)

      // Auto-expand and select first master.md (like GitHub README)
      if (tree && tree.length > 0) {
        console.log('Processing file tree with', tree.length, 'root nodes')
        const expandedPaths = new Set<string>()
        let firstMasterPath: string | null = null

        // Function to recursively find master.md and expand folders
        const findMasterAndExpand = (nodes: FileTreeNode[], parentPath: string = '') => {
          console.log('Processing nodes at level:', parentPath, 'nodes:', nodes.length)
          for (const node of nodes) {
            console.log('Processing node:', node.name, 'type:', node.type, 'path:', node.path)
            if (node.type === 'folder') {
              // Auto-expand all vault and context folders
              expandedPaths.add(node.path)
              console.log('Added to expanded paths:', node.path)

              if (node.children) {
                findMasterAndExpand(node.children, node.path)
              }
            } else if (node.type === 'file' && node.name === 'master.md' && !firstMasterPath) {
              // Found the first master.md
              firstMasterPath = node.path
              console.log('Found first master.md at:', firstMasterPath)
            } else if (node.type === 'file') {
              console.log('Found file:', node.name, 'at:', node.path)
            }
          }
        }

        findMasterAndExpand(tree)

        // Update state with expanded folders and selected master.md
        console.log('Setting expanded folders:', Array.from(expandedPaths))
        setExpandedFolders(expandedPaths)

        if (firstMasterPath) {
          console.log('Setting selected file and switching to master mode:', firstMasterPath)
          setSelectedFile(firstMasterPath)

          // Use setTimeout to ensure state updates are processed
          setTimeout(() => {
            setViewMode(prev => {
              console.log('Switching view mode from', prev.currentMode, 'to master')
              return { ...prev, currentMode: 'master' }
            })
            // Load the master.md content
            loadMasterContent(firstMasterPath)
          }, 100)
        } else {
          console.log('No master.md found in file tree')
        }
      }
    } catch (err: any) {
      console.error('Error loading file tree:', err)
      setError(err.message || 'Failed to load file tree')
    } finally {
      setLoading(false)
    }
  }

  // Add CSS styles for file tree interactions
  React.useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      .file-tree-item {
        transition: all 0.2s ease;
      }
      .file-tree-item:hover {
        background-color: rgba(138, 176, 187, 0.1);
      }
      .file-tree-item.selected {
        background-color: rgba(138, 176, 187, 0.2);
        border-left: 2px solid #8AB0BB;
      }
      .file-tree-item.drop-target {
        background-color: rgba(138, 176, 187, 0.2);
        border: 2px dashed #8AB0BB;
      }
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    `
    document.head.appendChild(style)

    return () => {
      document.head.removeChild(style)
    }
  }, [])

  const toggleFolder = (path: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev)
      if (newSet.has(path)) {
        newSet.delete(path)
      } else {
        newSet.add(path)
      }
      return newSet
    })
  }

  const selectFile = (path: string) => {
    setSelectedFile(path)

    // If selecting master.md, load its content
    if (path.endsWith('master.md')) {
      loadMasterContent(path)
      setViewMode(prev => ({ ...prev, currentMode: 'master' }))
    }
  }

  const loadMasterContent = async (filePath: string) => {
    try {
      setMasterLoading(true)

      // Check if electronAPI is available
      if (!window.electronAPI?.vault?.readFile) {
        setMasterContent('# Master Document\n\nContent not available in development mode.')
        return
      }

      const result = await window.electronAPI.vault.readFile(filePath)
      if (result.success && result.content) {
        setMasterContent(result.content)
      } else {
        setMasterContent('# Error\n\nFailed to load master document content.')
      }
    } catch (error) {
      console.error('Error loading master content:', error)
      setMasterContent('# Error\n\nFailed to load master document content.')
    } finally {
      setMasterLoading(false)
    }
  }

  // File drop handlers
  const handleDragOver = (e: React.DragEvent, folderPath?: string) => {
    e.preventDefault()
    e.stopPropagation()
    setDragOver(folderPath || 'general')
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragOver(null)
  }

  const handleFileDrop = async (e: React.DragEvent, folderPath?: string) => {
    e.preventDefault()
    e.stopPropagation()
    setDragOver(null)

    const files = Array.from(e.dataTransfer.files)
    if (files.length === 0) return

    try {
      console.log(`Dropping ${files.length} files to folder:`, folderPath)

      if (!folderPath) {
        setToast({
          message: 'Please drop files on a specific folder',
          type: 'warning'
        })
        return
      }

      // Prepare files for copying
      const filesToCopy = files.map(file => ({
        path: file.path || '', // Note: file.path might not be available in browser
        name: file.name
      }))

      // Copy files to folder using vault API
      if (window.electronAPI?.vault) {
        // For files page, we need to determine the context path from the folder path
        // This is a simplified approach - in a real implementation, you'd need to
        // parse the folder path to find the context root
        const result = await window.electronAPI.vault.copyFilesToContext(
          filesToCopy,
          folderPath, // Using folder path directly for now
          '' // No subfolder since we're dropping directly into the target folder
        )

        if (result.success) {
          setToast({
            message: `Successfully added ${result.copiedFiles.length} files`,
            type: 'success'
          })

          // Refresh file tree after drop
          await loadFileTree()
        } else {
          setToast({
            message: `Error adding files: ${result.error}`,
            type: 'error'
          })
        }
      } else {
        setToast({
          message: 'File operations not available in browser mode',
          type: 'warning'
        })
      }
    } catch (error) {
      console.error('Error dropping files:', error)
      setToast({
        message: 'Error dropping files',
        type: 'error'
      })
    }
  }

  // New handler functions for file opening features
  const handleFileAction = (action: string, files: FileTreeNode[]) => {
    console.log('File action:', action, 'Files:', files)

    switch (action) {
      case 'open-primary':
      case 'edit-in-chatlo':
        if (files.length === 1) {
          setFileViewerModal({
            file: files[0],
            viewMode: action === 'edit-in-chatlo' ? 'edit' : 'preview'
          })
        }
        break

      case 'preview-in-chatlo':
      case 'quick-preview':
        if (files.length === 1) {
          setFileViewerModal({
            file: files[0],
            viewMode: 'preview'
          })
        }
        break

      case 'open-with-system':
        // TODO: Implement system file opening
        setToast({
          message: 'Opening with system default...',
          type: 'info'
        })
        break

      case 'copy-path':
        if (files.length === 1) {
          navigator.clipboard.writeText(files[0].path)
          setToast({
            message: 'File path copied to clipboard',
            type: 'success'
          })
        }
        break

      case 'download':
        setToast({
          message: 'Download started...',
          type: 'info'
        })
        break

      case 'share':
        setToast({
          message: 'Share functionality coming soon',
          type: 'info'
        })
        break

      case 'delete':
        setToast({
          message: `Delete functionality for ${files.length} file(s) coming soon`,
          type: 'warning'
        })
        break

      case 'select':
        if (files.length === 1) {
          handleFileSelect(files[0], false)
        }
        break

      default:
        console.log('Unhandled action:', action)
    }
  }

  const handleFileRightClick = (event: React.MouseEvent, file: FileTreeNode) => {
    event.preventDefault()
    event.stopPropagation()

    setContextMenu({
      file,
      position: { x: event.clientX, y: event.clientY }
    })
  }

  const handleFileDoubleClick = (file: FileTreeNode) => {
    if (file.type === 'file') {
      handleFileAction('open-primary', [file])
    } else {
      toggleFolder(file.path)
    }
  }

  const handleFileSelect = (file: FileTreeNode, isMultiSelect: boolean = false) => {
    if (isMultiSelect) {
      setSelectedFiles(prev => {
        const isAlreadySelected = prev.some(f => f.path === file.path)
        if (isAlreadySelected) {
          return prev.filter(f => f.path !== file.path)
        } else {
          return [...prev, file]
        }
      })
    } else {
      setSelectedFiles([file])
      setSelectedFile(file.path)

      // If it's a folder and we're in the main content area, navigate to it
      if (file.type === 'folder') {
        // Only navigate if this is from main content, not sidebar
        // We'll handle this in the main content click handler
      }
    }
  }

  const handleMainContentFileClick = (file: FileTreeNode, isMultiSelect: boolean = false) => {
    if (file.type === 'folder') {
      navigateToFolder(file.path)
    } else {
      handleFileSelect(file, isMultiSelect)
    }
  }

  const handleMainContentFileDoubleClick = (file: FileTreeNode) => {
    if (file.type === 'file') {
      // Open file in default application or viewer
      handleFileAction('open-primary', [file])
    }
  }

  const getSelectedFileNode = (): FileTreeNode | null => {
    if (selectedFiles.length === 1) {
      return selectedFiles[0]
    }

    // Fallback to finding by path
    if (selectedFile) {
      const findNode = (nodes: FileTreeNode[]): FileTreeNode | null => {
        for (const node of nodes) {
          if (node.path === selectedFile) return node
          if (node.children) {
            const found = findNode(node.children)
            if (found) return found
          }
        }
        return null
      }
      return findNode(fileTree)
    }

    return null
  }

  const handleModeChange = (mode: 'explorer' | 'master') => {
    setViewMode(prev => ({
      ...prev,
      currentMode: mode,
      showArtifacts: mode === 'master'
    }))

    // Auto-select master.md when switching to master mode
    if (mode === 'master') {
      setSelectedFile('master.md')
    }
  }

  const renderFileTreeNode = (node: FileTreeNode, level: number = 0) => {
    const isExpanded = expandedFolders.has(node.path)
    const isSelected = selectedFile === node.path
    const marginLeft = level * 16 // 4 * 4 = 16px per level

    // Debug: Log expansion check
    if (node.type === 'folder') {
      console.log(`Checking expansion for ${node.name} (${node.path}): ${isExpanded}`)
      console.log('Available expanded paths:', Array.from(expandedFolders))
    }

    return (
      <div key={node.path}>
        <div
          className={`
            file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 transition-all relative
            ${isSelected ? 'selected bg-primary/20 border border-primary/30' : 'hover:bg-gray-700/50'}
            ${dragOver === node.path && node.type === 'folder' ? 'drop-target' : ''}
          `}
          style={{ marginLeft: `${marginLeft}px` }}
          onDragOver={node.type === 'folder' ? (e) => handleDragOver(e, node.path) : undefined}
          onDragLeave={node.type === 'folder' ? handleDragLeave : undefined}
          onDrop={node.type === 'folder' ? (e) => handleFileDrop(e, node.path) : undefined}
          onClick={(e) => {
            handleFileSelect(node, e.ctrlKey || e.metaKey)
            if (node.type === 'folder') {
              toggleFolder(node.path)
            }
          }}
          onDoubleClick={() => handleFileDoubleClick(node)}
          onContextMenu={(e) => handleFileRightClick(e, node)}
        >
          {node.type === 'folder' && (
            <Icon
              name={isExpanded ? 'chevronDown' : 'chevronRight'}
              className="text-gray-400"
              size="xs"
            />
          )}
          {node.type === 'file' && <div className="w-3"></div>}

          <FileTypeIcon
            file={{ ...node, isExpanded }}
            size="sm"
            showState={false}
          />
          
          <span className={`text-sm ${isSelected ? 'text-primary font-medium' : node.color === 'text-primary' ? 'text-primary font-medium' : 'text-supplement1'}`}>
            {node.name}
          </span>
          
          <div className="ml-auto">
            {node.fileCount && (
              <span className={`w-5 h-5 ${node.color === 'text-secondary' ? 'bg-secondary/20 text-secondary' : 'bg-supplement2/20 text-supplement2'} text-xs rounded-full flex items-center justify-center font-medium`}>
                {node.fileCount}
              </span>
            )}
            {isSelected && node.type === 'file' && (
              <div className="w-2 h-2 bg-primary rounded-full"></div>
            )}
          </div>
        </div>
        
        {node.type === 'folder' && isExpanded && node.children && (
          <div>
            {node.children.map(child => renderFileTreeNode(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col h-full bg-gray-900 text-white">
      {/* Main Files Content */}
      <div className="flex-1 flex bg-gray-900">
        
        {/* Left Column - File Tree (20%) */}
        <div className="w-1/5 bg-gray-800 border-r border-tertiary/50 flex flex-col">
          
          {/* File Tree Header */}
          <div className="p-4 border-b border-tertiary/50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Icon name="folderTree" className="text-supplement2" size="sm" />
                <h3 className="font-medium text-supplement1 text-sm">Files in Vault</h3>
              </div>
              <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                <Icon name="plus" className="text-gray-400" size="xs" />
                <div className="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                  Add File
                </div>
              </button>
            </div>
          </div>

          {/* View Toggle Buttons */}
          <div className="p-3 border-b border-tertiary/50">
            <div className="flex gap-2">
              <button 
                onClick={() => handleModeChange('explorer')}
                className={`flex-1 flex items-center justify-center gap-2 p-2 rounded-lg transition-colors ${
                  viewMode.currentMode === 'explorer' 
                    ? 'bg-secondary text-gray-900 hover:bg-secondary/80' 
                    : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
                }`}
              >
                <Icon name="th" size="sm" />
                <span className="text-xs font-medium">Explorer</span>
              </button>
              <button
                onClick={() => handleModeChange('master')}
                className={`flex-1 flex items-center justify-center gap-2 p-2 rounded-lg transition-colors ${
                  viewMode.currentMode === 'master'
                    ? 'bg-secondary text-gray-900 hover:bg-secondary/80'
                    : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
                }`}
              >
                <Icon name="lightbulb" size="sm" />
                <span className="text-xs font-medium">Master</span>
              </button>
            </div>
          </div>
          
          {/* File Tree */}
          <div className="flex-1 overflow-y-auto p-2">
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <Icon name="sync" className="text-primary mb-2" size="lg" spin />
                  <p className="text-sm text-supplement1">Loading files...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <p className="text-sm text-secondary mb-2">Error: {error}</p>
                  <button
                    onClick={loadFileTree}
                    className="px-3 py-1 bg-primary text-gray-900 rounded text-xs hover:bg-primary/80 transition-colors"
                  >
                    Retry
                  </button>
                </div>
              </div>
            ) : (
              fileTree.map(node => renderFileTreeNode(node))
            )}
          </div>
        </div>
        
        {/* Right Column Content (80%) - Based on design specs */}
        <div className="flex-1 flex flex-col">
          {(() => {
            console.log('🔍 DEBUG: Rendering right column, viewMode.currentMode:', viewMode.currentMode)
            console.log('🔍 DEBUG: Master content length:', masterContent.length)
            console.log('🔍 DEBUG: Master loading:', masterLoading)

            return viewMode.currentMode === 'master' ? (
              // Master View: 60% markdown + 40% recent chats (as per design)
              <>
                {/* Top Row - Master.md Preview (60%) */}
                <div className="h-[60%] flex border-b border-tertiary/50">
                  <MasterMode
                    content={masterContent}
                    loading={masterLoading}
                    selectedFile={selectedFile}
                  />

                  {/* Prompt Action Box (as per design) */}
                  <div className="w-80 bg-gray-800 border-l border-tertiary/50 p-4 flex flex-col">
                    <div className="mb-4">
                      <h4 className="font-medium text-supplement1 mb-2 text-sm">Quick Actions</h4>
                    </div>

                    <div className="space-y-2 mb-6">
                      <button className="w-full p-2 bg-primary/20 border border-primary/50 rounded-lg text-left hover:bg-primary/30 transition-colors">
                        <div className="flex items-center gap-2">
                          <Icon name="circleQuestion" className="text-primary" size="sm" />
                          <div>
                            <p className="text-xs font-medium text-supplement1">Ask about this file</p>
                            <p className="text-xs text-gray-400">Get insights</p>
                          </div>
                        </div>
                      </button>

                      <button className="w-full p-2 bg-secondary/20 border border-secondary/50 rounded-lg text-left hover:bg-secondary/30 transition-colors">
                        <div className="flex items-center gap-2">
                          <Icon name="compress" className="text-secondary" size="sm" />
                          <div>
                            <p className="text-xs font-medium text-supplement1">Summarize</p>
                            <p className="text-xs text-gray-400">Brief summary</p>
                          </div>
                        </div>
                      </button>

                      <button className="w-full p-2 bg-supplement2/20 border border-supplement2/50 rounded-lg text-left hover:bg-supplement2/30 transition-colors">
                        <div className="flex items-center gap-2">
                          <Icon name="edit" className="text-supplement2" size="sm" />
                          <div>
                            <p className="text-xs font-medium text-supplement1">Edit content</p>
                            <p className="text-xs text-gray-400">Make improvements</p>
                          </div>
                        </div>
                      </button>
                    </div>

                    <div className="border-t border-tertiary/50 pt-4">
                      <textarea
                        placeholder="Ask anything about this file..."
                        className="w-full h-20 bg-gray-700 border border-tertiary/50 rounded-lg p-3 text-xs text-supplement1 placeholder-gray-400 resize-none focus:outline-none focus:border-primary/50"
                      />
                      <button className="w-full mt-3 p-2 bg-primary text-gray-900 rounded-lg font-medium hover:bg-primary/80 transition-colors text-sm">
                        Send
                      </button>
                    </div>
                  </div>
                </div>

                {/* Bottom Row - Recent Chats (40%) */}
                <div className="h-[40%] bg-gray-800/50">
                  <div className="p-4 border-b border-tertiary/50">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-supplement1">Recent Chats</h3>
                      <button className="text-xs text-primary hover:text-primary/80 transition-colors">View All</button>
                    </div>
                  </div>

                  <div className="flex-1 overflow-y-auto p-4">
                    <div className="space-y-3">
                      <div className="p-4 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1">
                            <h5 className="font-medium text-sm text-supplement1 mb-1">Component Architecture Discussion</h5>
                            <p className="text-xs text-gray-400 line-clamp-2">How should we structure the component hierarchy for better maintainability?</p>
                          </div>
                          <div className="flex items-center gap-2 ml-3">
                            <span className="text-xs text-gray-500">2h ago</span>
                            <button className="p-1 hover:bg-gray-600 rounded transition-colors">
                              <Icon name="reply" className="text-gray-400" size="xs" />
                            </button>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-primary rounded-full"></div>
                          <span className="text-xs text-gray-400">12 messages</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              // Explorer View: Full height file explorer
              <ExplorerMode
                fileTree={fileTree}
                loading={loading}
                selectedFile={getSelectedFileNode()}
                selectedFiles={selectedFiles}
                currentFolder={currentFolder}
                viewType={viewType}
                sortBy={sortBy}
                sortOrder={sortOrder}
                breadcrumbs={breadcrumbs}
                showPreview={showPreview}
                onFileClick={handleMainContentFileClick}
                onFileDoubleClick={handleMainContentFileDoubleClick}
                onFolderNavigate={navigateToFolder}
                onViewTypeChange={setViewType}
                onSortChange={(newSortBy) => {
                  if (newSortBy === sortBy) {
                    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
                  } else {
                    setSortBy(newSortBy)
                    setSortOrder('asc')
                  }
                }}
                onPreviewToggle={togglePreview}
                getCurrentFolderContents={getCurrentFolderContents}
                sortFiles={sortFiles}
              />
            )
          })()}
        </div>
      </div>

      {/* File Actions Toolbar */}
      {selectedFiles.length > 0 && (
        <FileActionsToolbar
          selectedFiles={selectedFiles}
          onAction={handleFileAction}
          className="border-t border-neutral-700"
        />
      )}

      {/* Context Menu */}
      {contextMenu && (
        <FileContextMenu
          file={contextMenu.file}
          position={contextMenu.position}
          isVisible={true}
          onClose={() => setContextMenu(null)}
          onAction={(action, file) => {
            handleFileAction(action, [file])
            setContextMenu(null)
          }}
        />
      )}

      {/* File Viewer Modal */}
      {fileViewerModal && (
        <FileViewerModal
          file={fileViewerModal.file}
          isOpen={true}
          onClose={() => setFileViewerModal(null)}
          onAction={handleFileAction}
          initialViewMode={fileViewerModal.viewMode}
        />
      )}

      {/* Toast Notifications */}
      {toast && (
        <div className="fixed bottom-4 right-4 z-50">
          <div className={`
            px-4 py-3 rounded-lg shadow-lg border flex items-center gap-3 min-w-64
            ${toast.type === 'success' ? 'bg-green-600 border-green-500 text-white' :
              toast.type === 'error' ? 'bg-red-600 border-red-500 text-white' :
              toast.type === 'warning' ? 'bg-yellow-600 border-yellow-500 text-white' :
              'bg-blue-600 border-blue-500 text-white'
            }
          `}>
            <Icon
              name={
                toast.type === 'success' ? 'check' :
                toast.type === 'error' ? 'exclamationTriangle' :
                toast.type === 'warning' ? 'exclamationTriangle' :
                'infoCircle'
              }
              size="sm"
            />
            <span className="flex-1">{toast.message}</span>
            <button
              onClick={() => setToast(null)}
              className="text-white/80 hover:text-white transition-colors"
            >
              <Icon name="timesIcon" size="xs" />
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

// Master Mode Component
interface MasterModeProps {
  content: string
  loading: boolean
  selectedFile: string | null
}

const MasterMode: React.FC<MasterModeProps> = ({ content, loading, selectedFile }) => {
  // Extract title from content (first # heading)
  const getTitle = (content: string) => {
    const match = content.match(/^#\s+(.+)$/m)
    return match ? match[1] : 'Master Document'
  }

  // Simple markdown to HTML conversion for basic display
  const renderMarkdown = (content: string) => {
    if (!content) return ''

    return content
      .replace(/^# (.+)$/gm, '<h1 class="text-2xl font-bold text-supplement1 mb-4">$1</h1>')
      .replace(/^## (.+)$/gm, '<h2 class="text-xl font-semibold text-supplement1 mb-3 mt-6">$2</h2>')
      .replace(/^### (.+)$/gm, '<h3 class="text-lg font-medium text-supplement2 mb-2 mt-4">$3</h3>')
      .replace(/^\* (.+)$/gm, '<li class="text-gray-400 ml-4">• $1</li>')
      .replace(/^- (.+)$/gm, '<li class="text-gray-400 ml-4">• $1</li>')
      .replace(/\*\*(.+?)\*\*/g, '<strong class="text-supplement1">$1</strong>')
      .replace(/\*(.+?)\*/g, '<em class="text-gray-300">$1</em>')
      .replace(/`(.+?)`/g, '<code class="bg-gray-800 px-1 py-0.5 rounded text-primary text-sm">$1</code>')
      .replace(/\n\n/g, '</p><p class="text-gray-400 mb-4">')
      .replace(/^(?!<[h|l])/gm, '<p class="text-gray-400 mb-4">')
      .replace(/<p class="text-gray-400 mb-4">(<[h|l])/g, '$1')
  }

  return (
    <div className="flex-1 flex">
      {/* Master Document Preview */}
      <div className="flex-1 overflow-y-auto p-6">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <Icon name="sync" className="text-primary mb-4" size="2xl" spin />
              <p className="text-supplement1">Loading master document...</p>
            </div>
          </div>
        ) : (
          <div className="markdown-content">
            {/* Document Header */}
            <div className="flex items-center gap-3 mb-6 pb-4 border-b border-tertiary/30">
              <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                <Icon name="fileText" className="text-primary" size="lg" />
              </div>
              <div>
                <h1 className="text-supplement1 text-xl font-semibold">{getTitle(content)}</h1>
                <div className="flex items-center gap-4 text-xs text-gray-400 mt-1">
                  <span className="flex items-center gap-1">
                    <Icon name="clock" size="xs" />
                    Just updated
                  </span>
                  <span className="flex items-center gap-1">
                    <Icon name="fileText" size="xs" />
                    {selectedFile ? selectedFile.split('/').pop() : 'master.md'}
                  </span>
                </div>
              </div>
            </div>

            {/* Markdown Content */}
            <div
              className="prose prose-invert max-w-none"
              dangerouslySetInnerHTML={{ __html: renderMarkdown(content) }}
            />
          </div>
        )}
      </div>

      {/* Artifacts Sidebar will be rendered by ArtifactsSidebar component */}
      {/* <ArtifactsSidebar /> */}
    </div>
  )
}

// Explorer Mode Component - Windows Explorer Style
interface ExplorerModeProps {
  fileTree: FileTreeNode[]
  loading: boolean
  selectedFile: FileTreeNode | null
  selectedFiles: FileTreeNode[]
  currentFolder: string | null
  viewType: 'list' | 'grid'
  sortBy: 'name' | 'size' | 'date' | 'type'
  sortOrder: 'asc' | 'desc'
  breadcrumbs: Array<{name: string, path: string}>
  showPreview: boolean
  onFileClick: (file: FileTreeNode, isMultiSelect: boolean) => void
  onFileDoubleClick: (file: FileTreeNode) => void
  onFolderNavigate: (folderPath: string | null) => void
  onViewTypeChange: (type: 'list' | 'grid') => void
  onSortChange: (by: 'name' | 'size' | 'date' | 'type') => void
  onPreviewToggle: () => void
  getCurrentFolderContents: () => FileTreeNode[]
  sortFiles: (files: FileTreeNode[]) => FileTreeNode[]
}

const ExplorerMode: React.FC<ExplorerModeProps> = ({
  fileTree,
  loading,
  selectedFile,
  selectedFiles,
  currentFolder,
  viewType,
  sortBy,
  sortOrder,
  breadcrumbs,
  showPreview,
  onFileClick,
  onFileDoubleClick,
  onFolderNavigate,
  onViewTypeChange,
  onSortChange,
  onPreviewToggle,
  getCurrentFolderContents,
  sortFiles
}) => {

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString()
  }

  const renderBreadcrumbs = () => (
    <div className="flex items-center gap-1 text-sm text-neutral-400">
      <button
        onClick={() => onFolderNavigate(null)}
        className="hover:text-white transition-colors"
      >
        <Icon name="home" size="sm" />
      </button>
      {breadcrumbs.map((crumb, index) => (
        <React.Fragment key={crumb.path}>
          <Icon name="chevronRight" size="xs" className="text-neutral-600" />
          <button
            onClick={() => onFolderNavigate(crumb.path)}
            className="hover:text-white transition-colors"
          >
            {crumb.name}
          </button>
        </React.Fragment>
      ))}
    </div>
  )

  const renderFileInList = (file: FileTreeNode) => {
    const isSelected = selectedFiles.some(f => f.path === file.path)

    return (
      <div
        key={file.path}
        className={`
          flex items-center gap-3 p-2 rounded-lg cursor-pointer transition-colors
          ${isSelected ? 'bg-primary/20 border border-primary/30' : 'hover:bg-neutral-700/50'}
        `}
        onClick={(e) => onFileClick(file, e.ctrlKey || e.metaKey)}
        onDoubleClick={() => onFileDoubleClick(file)}
      >
        <FileTypeIcon file={file} size="sm" showState={false} />
        <span className="flex-1 text-sm text-neutral-300 truncate min-w-0">
          {file.name}
        </span>
        <span className="text-xs text-neutral-500 w-16 text-right">
          {file.type === 'file' ? formatFileSize(file.size || 0) : ''}
        </span>
        <span className="text-xs text-neutral-500 w-20 text-right">
          {file.modified ? formatDate(file.modified) : ''}
        </span>
      </div>
    )
  }

  const renderFileInGrid = (file: FileTreeNode) => {
    const isSelected = selectedFiles.some(f => f.path === file.path)

    return (
      <div
        key={file.path}
        className={`
          flex flex-col items-center p-4 rounded-lg cursor-pointer transition-colors
          ${isSelected ? 'bg-primary/20 border border-primary/30' : 'hover:bg-neutral-700/50'}
        `}
        onClick={(e) => onFileClick(file, e.ctrlKey || e.metaKey)}
        onDoubleClick={() => onFileDoubleClick(file)}
      >
        <FileTypeIcon file={file} size="lg" showState={false} />
        <span className="text-sm text-neutral-300 text-center mt-2 truncate w-full">
          {file.name}
        </span>
        {file.type === 'file' && (
          <span className="text-xs text-neutral-500 mt-1">
            {formatFileSize(file.size || 0)}
          </span>
        )}
      </div>
    )
  }

  // If no file tree data, show empty state
  if (fileTree.length === 0 && !loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center max-w-md">
          <Icon name="folder" className="text-supplement1 mb-6" size="2xl" />
          <h2 className="text-2xl font-semibold text-supplement1 mb-4">No Context Vaults Found</h2>
          <p className="text-gray-400 mb-8 leading-relaxed">
            You need to set up your context vaults first to organize and manage your files.
            Context vaults help you organize documents, conversations, and AI insights by topic or project.
          </p>
          <button
            onClick={() => window.location.href = '/settings?tab=data'}
            className="px-8 py-4 bg-primary text-gray-900 rounded-lg font-semibold hover:bg-primary/80 transition-colors text-lg"
          >
            Set Up Vaults
          </button>
        </div>
      </div>
    )
  }

  // Show loading state
  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <Icon name="sync" className="text-primary mb-4" size="2xl" spin />
          <p className="text-supplement1 text-lg">Loading vault contents...</p>
        </div>
      </div>
    )
  }

  const currentFiles = sortFiles(getCurrentFolderContents())

  // Show Windows Explorer-style file manager
  return (
    <div className={`flex-1 flex ${showPreview ? '' : 'pr-0'}`}>
      {/* Main Content Area */}
      <div className={`flex flex-col bg-neutral-900/50 transition-all duration-300 ${showPreview ? 'flex-1' : 'w-full'}`}>

        {/* Toolbar */}
        <div className="p-4 border-b border-neutral-700">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-4">
              <h3 className="text-lg font-medium text-white">File Explorer</h3>
              {breadcrumbs.length > 0 && (
                <div className="text-sm text-neutral-500">
                  {renderBreadcrumbs()}
                </div>
              )}
            </div>

            <div className="flex items-center gap-2">
              {/* View Type Toggle */}
              <div className="flex items-center gap-1 bg-neutral-800 rounded-lg p-1">
                <button
                  onClick={() => onViewTypeChange('list')}
                  className={`p-2 rounded transition-colors ${
                    viewType === 'list'
                      ? 'bg-primary text-gray-900'
                      : 'text-neutral-400 hover:text-white hover:bg-neutral-700'
                  }`}
                >
                  <Icon name="list" size="sm" />
                </button>
                <button
                  onClick={() => onViewTypeChange('grid')}
                  className={`p-2 rounded transition-colors ${
                    viewType === 'grid'
                      ? 'bg-primary text-gray-900'
                      : 'text-neutral-400 hover:text-white hover:bg-neutral-700'
                  }`}
                >
                  <Icon name="th" size="sm" />
                </button>
              </div>

              {/* Preview Toggle */}
              <button
                onClick={onPreviewToggle}
                className={`p-2 rounded-lg transition-colors ${
                  showPreview
                    ? 'bg-primary text-gray-900'
                    : 'text-neutral-400 hover:text-white hover:bg-neutral-700'
                }`}
                title="Toggle Preview Panel"
              >
                <Icon name="eye" size="sm" />
              </button>
            </div>
          </div>

          {/* Sort Controls */}
          <div className="flex items-center gap-2 text-sm">
            <span className="text-neutral-400">Sort by:</span>
            {(['name', 'size', 'date', 'type'] as const).map((sort) => (
              <button
                key={sort}
                onClick={() => onSortChange(sort)}
                className={`px-2 py-1 rounded transition-colors ${
                  sortBy === sort
                    ? 'bg-primary/20 text-primary'
                    : 'text-neutral-400 hover:text-white'
                }`}
              >
                {sort.charAt(0).toUpperCase() + sort.slice(1)}
                {sortBy === sort && (
                  <Icon
                    name={sortOrder === 'asc' ? 'sortUp' : 'sortDown'}
                    size="xs"
                    className="ml-1"
                  />
                )}
              </button>
            ))}
          </div>
        </div>

        {/* File Content Area */}
        <div className="flex-1 overflow-auto p-4">
          {currentFiles.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <Icon name="folder" className="text-neutral-500 mb-4" size="xl" />
                <div className="text-neutral-400">
                  {currentFolder ? 'This folder is empty' : 'No files in this context vault'}
                </div>
              </div>
            </div>
          ) : viewType === 'list' ? (
            <div className="space-y-1">
              {currentFiles.map(file => renderFileInList(file))}
            </div>
          ) : (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {currentFiles.map(file => renderFileInGrid(file))}
            </div>
          )}
        </div>
      </div>

      {/* File Preview Panel */}
      {showPreview && selectedFile && (
        <div className="w-96 border-l border-neutral-700 transition-all duration-300">
          <FilePreviewPanel
            selectedFile={selectedFiles.find(f => f.path === selectedFile) || null}
            onAction={(action, file) => {
              // Handle preview panel actions
              if (action === 'close-preview') {
                onPreviewToggle()
              }
            }}
            className="h-full"
          />
        </div>
      )}
    </div>
  )
}

export default FilesPage
