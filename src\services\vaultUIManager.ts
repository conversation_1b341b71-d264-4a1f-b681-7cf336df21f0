import { VaultRegistry, ContextVault, ContextFolder, ContextVaultCard, FileTreeNode } from '../types'
import { faFolder, faFolderOpen, faFile, faFileText, faImage, faCube } from '@fortawesome/free-solid-svg-icons'

/**
 * VaultUIManager Service
 * Handles scanning vaults, loading context data, and providing UI data structure
 */
export class VaultUIManager {
  private joinPath(...parts: string[]): string {
    const separator = navigator.platform.toLowerCase().includes('win') ? '\\' : '/'
    return parts.join(separator).replace(/[\/\\]+/g, separator)
  }

  /**
   * Get vault registry from storage
   */
  async getVaultRegistry(): Promise<VaultRegistry | null> {
    try {
      console.log('🔍 DEBUG: getVaultRegistry: Checking electronAPI availability...')
      console.log('🔍 DEBUG: window.electronAPI:', !!window.electronAPI)
      console.log('🔍 DEBUG: window.electronAPI.vault:', !!window.electronAPI?.vault)
      console.log('🔍 DEBUG: window.electronAPI.vault.getVaultRegistry:', !!window.electronAPI?.vault?.getVaultRegistry)

      // Check if electronAPI is available
      if (!window.electronAPI?.vault?.getVaultRegistry) {
        console.warn('🚨 DEBUG: Vault API not available - running in development mode')
        return null
      }

      console.log('🔍 DEBUG: getVaultRegistry: Calling electronAPI.vault.getVaultRegistry()...')
      const registry = await window.electronAPI.vault.getVaultRegistry()
      console.log('🔍 DEBUG: getVaultRegistry: Result from electronAPI:', registry)
      console.log('🔍 DEBUG: Registry type:', typeof registry)
      console.log('🔍 DEBUG: Registry vaults count:', registry?.vaults?.length || 0)

      if (registry?.vaults?.length > 0) {
        console.log('🔍 DEBUG: First vault:', registry.vaults[0])
      }

      return registry
    } catch (error) {
      console.error('🚨 DEBUG: Error loading vault registry:', error)
      return null
    }
  }

  /**
   * Scan all vaults and return updated registry
   */
  async scanVaults(): Promise<VaultRegistry | null> {
    try {
      const registry = await this.getVaultRegistry()
      if (!registry) return null

      // Scan each vault for updated context data
      const updatedVaults: ContextVault[] = []
      
      for (const vault of registry.vaults) {
        const updatedVault = await this.scanVault(vault)
        if (updatedVault) {
          updatedVaults.push(updatedVault)
        }
      }

      // Update registry
      const updatedRegistry: VaultRegistry = {
        ...registry,
        vaults: updatedVaults,
        lastScan: new Date().toISOString()
      }

      // Save updated registry
      await window.electronAPI.vault.saveVaultRegistry(updatedRegistry)
      
      return updatedRegistry
    } catch (error) {
      console.error('Error scanning vaults:', error)
      return null
    }
  }

  /**
   * Scan a single vault for updated context data
   */
  private async scanVault(vault: ContextVault): Promise<ContextVault | null> {
    try {
      // Check if vault path exists
      const pathCheck = await window.electronAPI.vault.pathExists(vault.path)
      if (!pathCheck.exists) {
        console.warn(`Vault path does not exist: ${vault.path}`)
        return null
      }

      // Scan vault directory for contexts
      const dirResult = await window.electronAPI.vault.readDirectory(vault.path)
      if (!dirResult.success) {
        console.error(`Failed to read vault directory: ${dirResult.error}`)
        return vault
      }

      // Find context folders (exclude system folders)
      const contextFolders = dirResult.items.filter(item => 
        item.isDirectory && !item.name.startsWith('.') && !item.name.startsWith('_')
      )

      // Scan each context folder
      const updatedContexts: ContextFolder[] = []
      for (const folder of contextFolders) {
        const context = await this.scanContext(folder.path, vault.color)
        if (context) {
          updatedContexts.push(context)
        }
      }

      return {
        ...vault,
        contexts: updatedContexts,
        lastAccessed: new Date().toISOString()
      }
    } catch (error) {
      console.error(`Error scanning vault ${vault.name}:`, error)
      return vault
    }
  }

  /**
   * Scan a context folder for updated data
   */
  private async scanContext(contextPath: string, defaultColor: string): Promise<ContextFolder | null> {
    try {
      // Read context metadata
      const metadataPath = this.joinPath(contextPath, '.context', 'metadata.json')
      const metadataResult = await window.electronAPI.vault.readFile(metadataPath)
      
      let metadata: any = {}
      if (metadataResult.success && metadataResult.content) {
        try {
          metadata = JSON.parse(metadataResult.content)
        } catch (e) {
          console.warn(`Invalid metadata JSON in ${metadataPath}`)
        }
      }

      // Scan files in context
      const dirResult = await window.electronAPI.vault.readDirectory(contextPath)
      if (!dirResult.success) {
        return null
      }

      // Count files (exclude system folders)
      const files = dirResult.items.filter(item => 
        !item.name.startsWith('.') && !item.name.startsWith('_')
      )
      const fileCount = files.length

      // Check for master.md
      const masterPath = this.joinPath(contextPath, 'master.md')
      const masterExists = await window.electronAPI.vault.pathExists(masterPath)
      
      let masterDoc = {
        exists: false,
        path: 'master.md',
        preview: '',
        wordCount: 0,
        lastUpdated: new Date().toISOString()
      }

      if (masterExists.exists) {
        const masterResult = await window.electronAPI.vault.readFile(masterPath)
        if (masterResult.success && masterResult.content) {
          const content = masterResult.content
          masterDoc = {
            exists: true,
            path: 'master.md',
            preview: content.substring(0, 100).replace(/\n/g, ' ') + (content.length > 100 ? '...' : ''),
            wordCount: content.split(/\s+/).length,
            lastUpdated: new Date().toISOString()
          }
        }
      }

      // Determine context status
      let status: 'empty' | 'active' | 'growing' | 'archived' = 'empty'
      if (fileCount > 10) status = 'growing'
      else if (fileCount > 1) status = 'active'

      // Calculate total size
      let totalSize = 0
      for (const file of files) {
        if (file.size) totalSize += file.size
      }

      const contextName = metadata.name || contextPath.split(/[\/\\]/).pop() || 'Unknown Context'
      
      return {
        id: metadata.id || Math.random().toString(36).substr(2, 9),
        name: contextName,
        path: contextPath,
        description: metadata.description || `Context vault for ${contextName}`,
        color: metadata.color || defaultColor,
        icon: metadata.icon || 'fa-folder',
        status,
        stats: {
          fileCount,
          conversationCount: 0, // TODO: Count linked conversations
          lastModified: new Date().toISOString(),
          sizeBytes: totalSize
        },
        masterDoc,
        aiInsights: {
          suggestedActions: this.generateSuggestions(status, fileCount),
          contextType: metadata.contextType || 'project',
          readinessScore: Math.min(fileCount / 10, 1)
        }
      }
    } catch (error) {
      console.error(`Error scanning context ${contextPath}:`, error)
      return null
    }
  }

  /**
   * Convert context folders to vault cards for UI
   */
  async getVaultCards(): Promise<ContextVaultCard[]> {
    try {
      const registry = await this.scanVaults()
      if (!registry) return []

      const cards: ContextVaultCard[] = []

      for (const vault of registry.vaults) {
        for (const context of vault.contexts) {
          cards.push(this.contextToCard(context))
        }
      }

      return cards
    } catch (error) {
      console.error('Error getting vault cards:', error)
      return []
    }
  }

  /**
   * Convert context folder to vault card
   */
  private contextToCard(context: ContextFolder): ContextVaultCard {
    return {
      id: context.id,
      name: context.name,
      description: context.description,
      color: context.color,
      icon: context.icon,
      status: context.status,
      fileCount: context.stats.fileCount,
      conversationCount: context.stats.conversationCount,
      lastActivity: this.formatLastActivity(context.stats.lastModified),
      masterPreview: context.masterDoc.preview,
      recentFiles: [], // TODO: Get recent files
      suggestedActions: context.aiInsights.suggestedActions,
      contextType: context.aiInsights.contextType,
      readinessScore: context.aiInsights.readinessScore,
      quickActions: this.getQuickActions(context.status, context.stats.fileCount)
    }
  }

  /**
   * Get file tree for Files page
   */
  async getFileTree(contextId?: string): Promise<FileTreeNode[]> {
    try {
      console.log('🔍 DEBUG: getFileTree: Loading vault registry...')
      console.log('🔍 DEBUG: getFileTree: contextId:', contextId)

      const registry = await this.getVaultRegistry()
      console.log('🔍 DEBUG: getFileTree: Registry loaded:', registry)
      console.log('🔍 DEBUG: getFileTree: Registry vaults:', registry?.vaults?.length || 0)

      if (!registry) {
        console.log('🚨 DEBUG: getFileTree: No registry found, returning empty array')
        return []
      }

      // If contextId specified, return tree for that context
      if (contextId) {
        console.log('🔍 DEBUG: getFileTree: Getting context file tree for:', contextId)
        return await this.getContextFileTree(contextId, registry)
      }

      // Return tree for all vaults
      console.log('🔍 DEBUG: getFileTree: Building tree for all vaults...')
      const tree: FileTreeNode[] = []

      for (const vault of registry.vaults) {
        console.log('🔍 DEBUG: getFileTree: Processing vault:', vault.name, 'with', vault.contexts?.length || 0, 'contexts')

        const vaultNode: FileTreeNode = {
          type: 'folder',
          name: vault.name,
          path: vault.path,
          icon: faFolder,
          color: vault.color,
          children: []
        }

        if (vault.contexts) {
          for (const context of vault.contexts) {
            console.log('🔍 DEBUG: getFileTree: Building context tree for:', context.name)
            const contextNode = await this.buildContextTree(context)
            if (contextNode) {
              console.log('🔍 DEBUG: getFileTree: Context node built successfully:', contextNode.name)
              vaultNode.children!.push(contextNode)
            } else {
              console.log('🚨 DEBUG: getFileTree: Failed to build context node for:', context.name)
            }
          }
        }

        console.log('🔍 DEBUG: getFileTree: Vault node completed with', vaultNode.children?.length || 0, 'children')
        tree.push(vaultNode)
      }

      console.log('🔍 DEBUG: getFileTree: Final tree length:', tree.length)
      return tree
    } catch (error) {
      console.error('🚨 DEBUG: Error getting file tree:', error)
      return []
    }
  }

  /**
   * Build file tree for a context
   */
  private async buildContextTree(context: ContextFolder): Promise<FileTreeNode | null> {
    try {
      console.log('🔍 DEBUG: buildContextTree: Building tree for context:', context.name, 'at path:', context.path)

      const dirResult = await window.electronAPI.vault.readDirectory(context.path)
      console.log('🔍 DEBUG: buildContextTree: readDirectory result:', dirResult)

      if (!dirResult.success) {
        console.log('🚨 DEBUG: buildContextTree: Failed to read directory:', dirResult.error)
        return null
      }

      const contextNode: FileTreeNode = {
        type: 'folder',
        name: context.name,
        path: context.path,
        icon: faFolderOpen,
        color: context.color,
        children: []
      }

      console.log('🔍 DEBUG: buildContextTree: Found', dirResult.items.length, 'items in context')

      // Add master.md first if it exists
      if (context.masterDoc.exists) {
        contextNode.children!.push({
          type: 'file',
          name: 'master.md',
          path: this.joinPath(context.path, 'master.md'),
          icon: faFileText,
          color: 'text-primary'
        })
      }

      // Add other files and folders
      for (const item of dirResult.items) {
        console.log('🔍 DEBUG: buildContextTree: Processing item:', item.name, 'isDirectory:', item.isDirectory)

        if (item.name.startsWith('.') || item.name === 'master.md') {
          console.log('🔍 DEBUG: buildContextTree: Skipping system file:', item.name)
          continue
        }

        const node: FileTreeNode = {
          type: item.isDirectory ? 'folder' : 'file',
          name: item.name,
          path: item.path,
          icon: this.getFileIcon(item.name, item.isDirectory),
          color: this.getFileColor(item.name, item.isDirectory),
          size: item.size,
          modified: item.modified
        }

        if (item.isDirectory) {
          // TODO: Recursively build folder contents if needed
          node.children = []
        }

        console.log('🔍 DEBUG: buildContextTree: Adding node:', node.name, 'type:', node.type)
        contextNode.children!.push(node)
      }

      console.log('🔍 DEBUG: buildContextTree: Context tree built with', contextNode.children?.length || 0, 'children')
      return contextNode

      return contextNode
    } catch (error) {
      console.error(`Error building context tree for ${context.name}:`, error)
      return null
    }
  }

  /**
   * Get context file tree for specific context
   */
  private async getContextFileTree(contextId: string, registry: VaultRegistry): Promise<FileTreeNode[]> {
    // Find the context
    let targetContext: ContextFolder | null = null
    for (const vault of registry.vaults) {
      const context = vault.contexts.find(c => c.id === contextId)
      if (context) {
        targetContext = context
        break
      }
    }

    if (!targetContext) return []

    const contextTree = await this.buildContextTree(targetContext)
    return contextTree ? [contextTree] : []
  }

  /**
   * Generate suggestions based on context state
   */
  private generateSuggestions(status: string, fileCount: number): string[] {
    if (status === 'empty') {
      return [
        '📁 Drop files here to get started',
        '💬 Start a conversation about your goals',
        '🎯 Tell me what you\'re working on'
      ]
    } else if (status === 'active') {
      return [
        '📋 Organize your files into folders',
        '🤖 Ask AI to analyze your content',
        '📝 Update your master document'
      ]
    } else {
      return [
        '🔍 Search across all your files',
        '📊 Generate project insights',
        '🎯 Plan your next steps'
      ]
    }
  }

  /**
   * Get quick actions based on context state
   */
  private getQuickActions(status: string, fileCount: number): { primary: { label: string; action: string }; secondary: { label: string; action: string } } {
    if (status === 'empty') {
      return {
        primary: { label: 'Add Files', action: 'open-file-picker' },
        secondary: { label: 'Start Chat', action: 'open-chat-with-context' }
      }
    } else {
      return {
        primary: { label: 'Browse Files', action: 'open-files-page' },
        secondary: { label: 'Continue Chat', action: 'open-chat-with-context' }
      }
    }
  }

  /**
   * Format last activity timestamp
   */
  private formatLastActivity(timestamp: string): string {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)

    if (diffHours < 1) return 'Just now'
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
    return date.toLocaleDateString()
  }

  /**
   * Get appropriate icon for file/folder
   */
  private getFileIcon(name: string, isDirectory: boolean) {
    if (isDirectory) {
      if (name === 'documents') return faFileText
      if (name === 'images') return faImage
      if (name === 'artifacts') return faCube
      return faFolder
    }

    const ext = name.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'md':
      case 'txt':
        return faFileText
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return faImage
      default:
        return faFile
    }
  }

  /**
   * Get appropriate color for file/folder
   */
  private getFileColor(name: string, isDirectory: boolean): string {
    if (isDirectory) {
      if (name === 'documents') return 'text-blue-400'
      if (name === 'images') return 'text-green-400'
      if (name === 'artifacts') return 'text-purple-400'
      return 'text-supplement1'
    }

    const ext = name.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'md':
        return 'text-primary'
      case 'txt':
        return 'text-supplement1'
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'text-green-400'
      default:
        return 'text-supplement1'
    }
  }
}

export const vaultUIManager = new VaultUIManager()
