import React, { useState, useRef, useEffect } from 'react'
import { Icon } from '../icons/FontAwesomeIcons'
import { FileTreeNode } from '../../types'

interface ImageViewerProps {
  file: FileTreeNode
  className?: string
}

const ImageViewer: React.FC<ImageViewerProps> = ({
  file,
  className = ''
}) => {
  const [zoom, setZoom] = useState(100)
  const [rotation, setRotation] = useState(0)
  const [fitMode, setFitMode] = useState<'fit' | 'fill' | 'actual'>('fit')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [imageData, setImageData] = useState<string | null>(null)
  const [imageDimensions, setImageDimensions] = useState<{ width: number; height: number } | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 })
  const imageRef = useRef<HTMLImageElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    loadImage()
  }, [file])

  const loadImage = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      // Simulate loading image
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // Create a mock image data URL
      const mockImageData = `data:image/svg+xml;base64,${btoa(`
        <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#8AB0BB;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#1B3E68;stop-opacity:1" />
            </linearGradient>
          </defs>
          <rect width="800" height="600" fill="url(#grad1)"/>
          <circle cx="400" cy="300" r="100" fill="#FF8383" opacity="0.8"/>
          <text x="400" y="320" text-anchor="middle" fill="white" font-family="Arial" font-size="24" font-weight="bold">
            ${file.name}
          </text>
          <text x="400" y="350" text-anchor="middle" fill="white" font-family="Arial" font-size="16">
            Sample Image Preview
          </text>
        </svg>
      `)}`
      
      setImageData(mockImageData)
      setImageDimensions({ width: 800, height: 600 })
    } catch (err) {
      setError('Failed to load image')
    } finally {
      setIsLoading(false)
    }
  }

  const handleZoomIn = () => {
    setZoom(prev => Math.min(500, prev + 25))
  }

  const handleZoomOut = () => {
    setZoom(prev => Math.max(10, prev - 25))
  }

  const handleRotateLeft = () => {
    setRotation(prev => (prev - 90) % 360)
  }

  const handleRotateRight = () => {
    setRotation(prev => (prev + 90) % 360)
  }

  const handleFitToScreen = () => {
    setFitMode('fit')
    setZoom(100)
    setImagePosition({ x: 0, y: 0 })
  }

  const handleActualSize = () => {
    setFitMode('actual')
    setZoom(100)
    setImagePosition({ x: 0, y: 0 })
  }

  const handleMouseDown = (e: React.MouseEvent) => {
    if (zoom > 100 || fitMode === 'actual') {
      setIsDragging(true)
      setDragStart({ x: e.clientX - imagePosition.x, y: e.clientY - imagePosition.y })
    }
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setImagePosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      })
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const getImageStyle = () => {
    const baseStyle: React.CSSProperties = {
      transform: `rotate(${rotation}deg) translate(${imagePosition.x}px, ${imagePosition.y}px)`,
      cursor: isDragging ? 'grabbing' : (zoom > 100 || fitMode === 'actual') ? 'grab' : 'default',
      transition: isDragging ? 'none' : 'transform 0.2s ease'
    }

    if (fitMode === 'fit') {
      return {
        ...baseStyle,
        maxWidth: '100%',
        maxHeight: '100%',
        width: 'auto',
        height: 'auto'
      }
    } else if (fitMode === 'fill') {
      return {
        ...baseStyle,
        width: '100%',
        height: '100%',
        objectFit: 'cover' as const
      }
    } else {
      return {
        ...baseStyle,
        width: `${(zoom / 100) * (imageDimensions?.width || 800)}px`,
        height: `${(zoom / 100) * (imageDimensions?.height || 600)}px`
      }
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center bg-neutral-900 ${className}`}>
        <div className="text-center">
          <Icon name="spinner" className="text-primary mb-4" size="xl" spin />
          <div className="text-neutral-300">Loading image...</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center bg-neutral-900 ${className}`}>
        <div className="text-center">
          <Icon name="exclamationTriangle" className="text-red-400 mb-4" size="xl" />
          <div className="text-red-300 mb-2">Failed to load image</div>
          <div className="text-neutral-400 text-sm">{error}</div>
        </div>
      </div>
    )
  }

  return (
    <div className={`flex flex-col bg-neutral-900 ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-3 border-b border-neutral-700 bg-neutral-800/50">
        <div className="flex items-center gap-3">
          {/* Zoom controls */}
          <div className="flex items-center gap-1">
            <button
              onClick={handleZoomOut}
              className="p-1.5 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded transition-colors"
              title="Zoom out"
            >
              <Icon name="minus" size="xs" />
            </button>
            <span className="text-xs text-neutral-400 min-w-12 text-center">{zoom}%</span>
            <button
              onClick={handleZoomIn}
              className="p-1.5 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded transition-colors"
              title="Zoom in"
            >
              <Icon name="plus" size="xs" />
            </button>
          </div>

          <div className="w-px h-4 bg-neutral-600" />

          {/* Rotation controls */}
          <button
            onClick={handleRotateLeft}
            className="p-1.5 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded transition-colors"
            title="Rotate left"
          >
            <Icon name="rotateLeft" size="xs" />
          </button>
          <button
            onClick={handleRotateRight}
            className="p-1.5 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded transition-colors"
            title="Rotate right"
          >
            <Icon name="rotateRight" size="xs" />
          </button>

          <div className="w-px h-4 bg-neutral-600" />

          {/* Fit controls */}
          <button
            onClick={handleFitToScreen}
            className={`p-1.5 rounded transition-colors ${
              fitMode === 'fit'
                ? 'text-primary bg-primary/10'
                : 'text-neutral-400 hover:text-white hover:bg-neutral-700'
            }`}
            title="Fit to screen"
          >
            <Icon name="expand" size="xs" />
          </button>
          <button
            onClick={handleActualSize}
            className={`p-1.5 rounded transition-colors ${
              fitMode === 'actual'
                ? 'text-primary bg-primary/10'
                : 'text-neutral-400 hover:text-white hover:bg-neutral-700'
            }`}
            title="Actual size"
          >
            <Icon name="maximize" size="xs" />
          </button>
        </div>

        <div className="flex items-center gap-3">
          {/* Image info */}
          {imageDimensions && (
            <div className="text-sm text-neutral-400">
              {imageDimensions.width} × {imageDimensions.height} pixels
            </div>
          )}

          {/* Action buttons */}
          <div className="flex items-center gap-2">
            <button
              onClick={() => navigator.clipboard.writeText(imageData || '')}
              className="p-1.5 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded transition-colors"
              title="Copy image"
            >
              <Icon name="copy" size="xs" />
            </button>
            <button
              className="p-1.5 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded transition-colors"
              title="Download image"
            >
              <Icon name="download" size="xs" />
            </button>
            <button
              className="p-1.5 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded transition-colors"
              title="Share image"
            >
              <Icon name="share" size="xs" />
            </button>
          </div>
        </div>
      </div>

      {/* Image container */}
      <div 
        ref={containerRef}
        className="flex-1 overflow-hidden bg-neutral-950 flex items-center justify-center relative"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {imageData && (
          <img
            ref={imageRef}
            src={imageData}
            alt={file.name}
            style={getImageStyle()}
            className="select-none"
            draggable={false}
          />
        )}

        {/* Zoom indicator */}
        {zoom !== 100 && (
          <div className="absolute top-4 left-4 bg-black/50 text-white px-2 py-1 rounded text-sm">
            {zoom}%
          </div>
        )}

        {/* Rotation indicator */}
        {rotation !== 0 && (
          <div className="absolute top-4 right-4 bg-black/50 text-white px-2 py-1 rounded text-sm">
            {rotation}°
          </div>
        )}
      </div>

      {/* Status bar */}
      <div className="flex items-center justify-between px-3 py-2 border-t border-neutral-700 bg-neutral-800/30 text-xs text-neutral-500">
        <div className="flex items-center gap-4">
          <span className="flex items-center gap-1">
            <Icon name="image" size="xs" />
            Image
          </span>
          {imageDimensions && (
            <>
              <span className="flex items-center gap-1">
                <Icon name="ruler" size="xs" />
                {imageDimensions.width} × {imageDimensions.height}
              </span>
              <span className="flex items-center gap-1">
                <Icon name="weight" size="xs" />
                {formatFileSize(Math.floor(Math.random() * 1000000) + 100000)}
              </span>
            </>
          )}
        </div>
        
        <div className="flex items-center gap-4">
          <span>Zoom: {zoom}%</span>
          <span>Rotation: {rotation}°</span>
          <span className="capitalize">Fit: {fitMode}</span>
        </div>
      </div>
    </div>
  )
}

export default ImageViewer
