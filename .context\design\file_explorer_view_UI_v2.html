<html><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>tailwind.config = {
  "theme": {
    "extend": {
      "colors": {
        "primary": "#8AB0BB",
        "secondary": "#FF8383",
        "tertiary": "#1B3E68",
        "supplement1": "#D5D8E0",
        "supplement2": "#89AFBA"
      },
      "fontFamily": {
        "sans": [
          "Inter",
          "sans-serif"
        ]
      }
    }
  }
};</script>
    <script> window.FontAwesomeConfig = { autoReplaceSvg: 'nest'};</script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <style>
        ::-webkit-scrollbar { display: none;}
        body { font-family: 'Inter', sans-serif; }
        .file-tree-item {
            transition: all 0.2s ease;
        }
        .file-tree-item:hover {
            background-color: rgba(138, 176, 187, 0.1);
        }
        .file-tree-item.selected {
            background-color: rgba(138, 176, 187, 0.2);
            border-left: 2px solid #8AB0BB;
        }
        .table-row:hover {
            background-color: rgba(138, 176, 187, 0.1);
        }
    </style>
<link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&amp;display=swap"><style>
  .highlighted-section {
    outline: 2px solid #3F20FB;
    background-color: rgba(63, 32, 251, 0.1);
  }

  .edit-button {
    position: absolute;
    z-index: 1000;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  html, body {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  </style></head>
<body class="bg-gray-900 text-white overflow-hidden">
    
    <!-- Window Top Bar -->
    <div id="window-top-bar" class="h-6 bg-gray-950 flex items-center justify-end px-2 border-b border-gray-800">
        <div class="flex items-center gap-1">
            <button class="w-4 h-4 flex items-center justify-center hover:bg-gray-800 rounded transition-colors">
                <i class="text-gray-400 text-xs fas fa-expand"></i>
            </button>
            <button class="w-4 h-4 flex items-center justify-center hover:bg-red-600 rounded transition-colors">
                <i class="text-gray-400 text-xs hover:text-white fas fa-xmark"></i>
            </button>
        </div>
    </div>
    
    <!-- Top Navigation Bar -->
    <div id="top-nav" class="h-12 bg-gray-800 border-b border-tertiary flex items-center px-4">
        <div class="flex items-center gap-2">
            <div class="w-6 h-6 bg-primary rounded flex items-center justify-center">
                <i class="text-gray-900 text-xs fas fa-comment"></i>
            </div>
            <span class="text-sm font-semibold text-primary">Chatlo</span>
        </div>
        
        <div class="flex-1 flex items-center justify-center ml-12 mr-[calc(256px-48px)]">
            <div class="flex items-center gap-2 bg-tertiary/40 rounded-lg px-3 py-1 w-full max-w-2xl">
                <button class="p-1 hover:bg-tertiary/60 rounded transition-colors">
                    <i class="text-supplement2 text-xs fas fa-bell"></i>
                </button>
                <button class="p-1 hover:bg-tertiary/60 rounded transition-colors bg-primary/20 border border-primary/30">
                    <i class="text-primary text-xs fas fa-file"></i>
                </button>
                <button class="p-1 hover:bg-tertiary/60 rounded transition-colors">
                    <i class="text-gray-400 text-xs fas fa-chevron-left"></i>
                </button>
                <div class="flex items-center gap-2 flex-1">
                    <i class="text-supplement2 text-xs fas fa-folder"></i>
                    <span class="text-xs text-supplement1">Project Alpha - Design System</span>
                </div>
                <button class="p-1 hover:bg-tertiary/60 rounded transition-colors">
                    <i class="text-gray-400 text-xs fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
        
        <div class="flex items-center gap-3">
            <div class="flex items-center gap-2">
                <span class="text-xs text-supplement1">Private</span>
                <button class="relative inline-flex h-4 w-7 items-center rounded-full bg-secondary transition-colors">
                    <span class="inline-block h-3 w-3 transform rounded-full bg-white transition-transform translate-x-3.5"></span>
                </button>
            </div>
            <button class="p-2 hover:bg-gray-700 rounded-lg transition-colors group relative">
                <i class="text-supplement1 text-sm fas fa-user"></i>
                <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    User Profile
                </div>
            </button>
            <button class="p-2 hover:bg-gray-700 rounded-lg transition-colors group relative">
                <i class="text-supplement1 text-sm fas fa-gear"></i>
                <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    Settings
                </div>
            </button>
        </div>
    </div>
    
    <div id="app-container" class="flex h-[calc(100vh-72px)]">
        
        <!-- VSCode-style Icon Bar -->
        <div id="icon-bar" class="w-12 bg-gray-900 border-r border-tertiary flex flex-col items-center py-2">
            <div class="flex flex-col gap-1 mb-auto">
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative">
                    <i class="fas fa-house"></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        Home
                    </div>
                </button>
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative">
                    <i class="fas fa-comment"></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        Chat
                    </div>
                </button>
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative">
                    <i class="fas fa-clock-rotate-left"></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        History
                    </div>
                </button>
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative bg-primary/20 border-l-2 border-primary">
                    <i class="text-primary fas fa-folder-tree"></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        Files
                    </div>
                </button>
            </div>
        </div>
        
        <!-- Main Files Content -->
        <div id="files-content" class="flex-1 flex bg-gray-900">
            
            <!-- Left Column - File Tree (20%) -->
            <div id="file-tree-panel" class="w-1/5 bg-gray-800 border-r border-tertiary/50 flex flex-col">
                
                <!-- File Tree Header -->
                <div class="p-4 border-b border-tertiary/50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2 flex-1">
                            <button class="p-1 hover:bg-gray-700 rounded transition-colors">
                                <i class="text-gray-400 text-xs fas fa-chevron-left"></i>
                            </button>
                            <select class="bg-transparent text-supplement1 text-sm font-medium border-none outline-none flex-1 cursor-pointer">
                                <option class="bg-gray-800 text-supplement1">Your First Context Vault</option>
                                <option class="bg-gray-800 text-supplement1">Project Beta Vault</option>
                                <option class="bg-gray-800 text-supplement1">Design System Vault</option>
                            </select>
                            <button class="p-1 hover:bg-gray-700 rounded transition-colors">
                                <i class="text-gray-400 text-xs fas fa-chevron-right"></i>
                            </button>
                        </div>
                        <button class="p-1 hover:bg-gray-700 rounded transition-colors group relative ml-2">
                            <i class="text-gray-400 text-xs fas fa-plus"></i>
                            <div class="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                                Add File
                            </div>
                        </button>
                    </div>
                </div>

                <!-- View Toggle Buttons -->
                <div class="p-3 border-b border-tertiary/50">
                    <div class="flex gap-2">
                        <button class="flex-1 flex items-center justify-center gap-2 p-2 rounded-lg bg-secondary text-gray-900 hover:bg-secondary/80 transition-colors">
                            <i class="text-sm fas fa-sitemap"></i>
                            <span class="text-xs font-medium">Explorer</span>
                        </button>
                        <button class="flex-1 flex items-center justify-center gap-2 p-2 rounded-lg bg-gray-700/50 hover:bg-gray-700 transition-colors text-supplement1">
                            <i class="text-sm fas fa-lightbulb"></i>
                            <span class="text-xs font-medium">Master</span>
                        </button>
                    </div>
                </div>
                
                <!-- File Tree -->
                <div id="file-tree" class="flex-1 overflow-y-auto p-2">
                    
                    <!-- Root Folder -->
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2" onclick="toggleFolder('root')">
                        <i class="text-gray-400 text-xs w-3 fas fa-chevron-down"></i>
                        <i class="text-supplement2 text-sm fas fa-folder"></i>
                        <span class="text-sm text-supplement1">project-alpha</span>
                        <div class="ml-auto">
                            <span class="w-5 h-5 bg-secondary/20 text-secondary text-xs rounded-full flex items-center justify-center font-medium">3</span>
                        </div>
                    </div>
                    
                    <!-- Master.md File -->
                    <div class="file-tree-item selected p-2 rounded cursor-pointer flex items-center gap-2 ml-4 bg-primary/20 border border-primary/30" onclick="selectFile('master.md')">
                        <div class="w-3"></div>
                        <i class="text-primary text-sm fas fa-file-lines"></i>
                        <span class="text-sm text-primary font-medium">master.md</span>
                        <div class="ml-auto">
                            <div class="w-2 h-2 bg-primary rounded-full"></div>
                        </div>
                    </div>
                    
                    <!-- Design Folder -->
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 ml-4" onclick="toggleFolder('design')">
                        <i class="text-gray-400 text-xs w-3 fas fa-chevron-right"></i>
                        <i class="text-supplement2 text-sm fas fa-folder"></i>
                        <span class="text-sm text-supplement1">design</span>
                        <div class="ml-auto">
                            <span class="w-4 h-4 bg-supplement2/20 text-supplement2 text-xs rounded-full flex items-center justify-center font-medium">2</span>
                        </div>
                    </div>
                    
                    <!-- Components Folder -->
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 ml-4" onclick="toggleFolder('components')">
                        <i class="text-gray-400 text-xs w-3 fas fa-chevron-down"></i>
                        <i class="text-supplement2 text-sm fas fa-folder"></i>
                        <span class="text-sm text-supplement1">components</span>
                        <div class="ml-auto">
                            <span class="w-4 h-4 bg-supplement2/20 text-supplement2 text-xs rounded-full flex items-center justify-center font-medium">2</span>
                        </div>
                    </div>
                    
                    <!-- Component Files -->
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 ml-8" onclick="selectFile('buttons.md')">
                        <div class="w-3"></div>
                        <i class="text-secondary text-sm fas fa-file-lines"></i>
                        <span class="text-sm text-secondary">buttons.md</span>
                        <div class="ml-auto">
                            <div class="w-2 h-2 bg-secondary rounded-full"></div>
                        </div>
                    </div>
                    
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 ml-8" onclick="selectFile('forms.md')">
                        <div class="w-3"></div>
                        <i class="text-gray-400 text-sm fas fa-file-lines"></i>
                        <span class="text-sm text-gray-300">forms.md</span>
                    </div>
                    
                    <!-- Tokens File -->
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 ml-4" onclick="selectFile('tokens.json')">
                        <div class="w-3"></div>
                        <i class="text-supplement2 text-sm fas fa-file-code"></i>
                        <span class="text-sm text-supplement1">tokens.json</span>
                    </div>
                    
                    <!-- Documentation Folder -->
                    <div class="file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 ml-4" onclick="toggleFolder('docs')">
                        <i class="text-gray-400 text-xs w-3 fas fa-chevron-right"></i>
                        <i class="text-supplement2 text-sm fas fa-folder"></i>
                        <span class="text-sm text-supplement1">docs</span>
                    </div>
                </div>
            </div>
            
            <!-- Right Column (80%) - File Explorer -->
            <div id="main-panel" class="flex-1 bg-gray-900 flex flex-col">
    
                <!-- Explorer Header with View Toggle -->
                <div class="border-b border-tertiary/50 p-4 flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-supplement1">File Explorer</h2>
                    <div class="flex items-center gap-2">
                        <button id="list-view-btn" class="p-2 rounded-lg bg-primary/20 text-primary hover:bg-primary/30 transition-colors" onclick="switchView('list')">
                            <i class="fas fa-list text-sm"></i>
                        </button>
                        <button id="icon-view-btn" class="p-2 rounded-lg bg-gray-700/50 hover:bg-gray-700 text-supplement1 transition-colors" onclick="switchView('icon')">
                            <i class="fas fa-th-large text-sm"></i>
                        </button>
                    </div>
                </div>
                
                <!-- List View -->
                <div id="list-view" class="flex-1 overflow-y-auto">
                    <table class="w-full">
                        <thead class="bg-gray-800 border-b border-tertiary/50">
                            <tr>
                                <th class="text-left p-4 text-sm font-medium text-supplement1">Name</th>
                                <th class="text-left p-4 text-sm font-medium text-supplement1">Date Modified</th>
                                <th class="text-left p-4 text-sm font-medium text-supplement1">Type</th>
                                <th class="text-left p-4 text-sm font-medium text-supplement1">Size</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="table-row border-b border-gray-800 cursor-pointer bg-primary/10 border-l-2 border-l-primary" onclick="selectFile('master.md')">
                                <td class="p-4">
                                    <div class="flex items-center gap-3">
                                        <i class="text-primary text-lg fas fa-file-lines"></i>
                                        <span class="text-primary font-medium">master.md</span>
                                    </div>
                                </td>
                                <td class="p-4 text-supplement1">2 hours ago</td>
                                <td class="p-4 text-gray-300">Markdown File</td>
                                <td class="p-4 text-gray-300">12.5 KB</td>
                            </tr>
                            
                            <tr class="table-row border-b border-gray-800 cursor-pointer" onclick="openFolder('design')">
                                <td class="p-4">
                                    <div class="flex items-center gap-3">
                                        <i class="text-supplement2 text-lg fas fa-folder"></i>
                                        <span class="text-supplement1 font-medium">design</span>
                                    </div>
                                </td>
                                <td class="p-4 text-supplement1">5 days ago</td>
                                <td class="p-4 text-gray-300">Folder</td>
                                <td class="p-4 text-gray-300">-</td>
                            </tr>
                            
                            <tr class="table-row border-b border-gray-800 cursor-pointer" onclick="openFolder('components')">
                                <td class="p-4">
                                    <div class="flex items-center gap-3">
                                        <i class="text-supplement2 text-lg fas fa-folder"></i>
                                        <span class="text-supplement1 font-medium">components</span>
                                    </div>
                                </td>
                                <td class="p-4 text-supplement1">4 hours ago</td>
                                <td class="p-4 text-gray-300">Folder</td>
                                <td class="p-4 text-gray-300">-</td>
                            </tr>
                            
                            <tr class="table-row border-b border-gray-800 cursor-pointer" onclick="selectFile('tokens.json')">
                                <td class="p-4">
                                    <div class="flex items-center gap-3">
                                        <i class="text-supplement2 text-lg fas fa-file-code"></i>
                                        <span class="text-supplement1 font-medium">tokens.json</span>
                                    </div>
                                </td>
                                <td class="p-4 text-supplement1">3 days ago</td>
                                <td class="p-4 text-gray-300">JSON File</td>
                                <td class="p-4 text-gray-300">8.2 KB</td>
                            </tr>
                            
                            <tr class="table-row border-b border-gray-800 cursor-pointer" onclick="openFolder('docs')">
                                <td class="p-4">
                                    <div class="flex items-center gap-3">
                                        <i class="text-supplement2 text-lg fas fa-folder"></i>
                                        <span class="text-supplement1 font-medium">docs</span>
                                    </div>
                                </td>
                                <td class="p-4 text-supplement1">1 week ago</td>
                                <td class="p-4 text-gray-300">Folder</td>
                                <td class="p-4 text-gray-300">-</td>
                            </tr>
                            
                            <tr class="table-row border-b border-gray-800 cursor-pointer" onclick="selectFile('README.md')">
                                <td class="p-4">
                                    <div class="flex items-center gap-3">
                                        <i class="text-gray-400 text-lg fas fa-file-lines"></i>
                                        <span class="text-supplement1 font-medium">README.md</span>
                                    </div>
                                </td>
                                <td class="p-4 text-supplement1">2 weeks ago</td>
                                <td class="p-4 text-gray-300">Markdown File</td>
                                <td class="p-4 text-gray-300">3.1 KB</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Icon View -->
                <div id="icon-view" class="flex-1 overflow-y-auto p-6 hidden">
                    <div class="grid grid-cols-4 xl:grid-cols-6 2xl:grid-cols-8 gap-4">
                        
                        <div class="flex flex-col items-center p-4 rounded-lg hover:bg-gray-800/50 cursor-pointer transition-colors bg-primary/10 border border-primary/30" onclick="selectFile('master.md')">
                            <i class="text-primary text-4xl fas fa-file-lines mb-3"></i>
                            <span class="text-sm text-primary font-medium text-center truncate w-full">master.md</span>
                            <span class="text-xs text-gray-400 mt-1">12.5 KB</span>
                        </div>
                        
                        <div class="flex flex-col items-center p-4 rounded-lg hover:bg-gray-800/50 cursor-pointer transition-colors" onclick="openFolder('design')">
                            <i class="text-supplement2 text-4xl fas fa-folder mb-3"></i>
                            <span class="text-sm text-supplement1 font-medium text-center truncate w-full">design</span>
                            <span class="text-xs text-gray-400 mt-1">Folder</span>
                        </div>
                        
                        <div class="flex flex-col items-center p-4 rounded-lg hover:bg-gray-800/50 cursor-pointer transition-colors" onclick="openFolder('components')">
                            <i class="text-supplement2 text-4xl fas fa-folder mb-3"></i>
                            <span class="text-sm text-supplement1 font-medium text-center truncate w-full">components</span>
                            <span class="text-xs text-gray-400 mt-1">Folder</span>
                        </div>
                        
                        <div class="flex flex-col items-center p-4 rounded-lg hover:bg-gray-800/50 cursor-pointer transition-colors" onclick="selectFile('tokens.json')">
                            <i class="text-supplement2 text-4xl fas fa-file-code mb-3"></i>
                            <span class="text-sm text-supplement1 font-medium text-center truncate w-full">tokens.json</span>
                            <span class="text-xs text-gray-400 mt-1">8.2 KB</span>
                        </div>
                        
                        <div class="flex flex-col items-center p-4 rounded-lg hover:bg-gray-800/50 cursor-pointer transition-colors" onclick="openFolder('docs')">
                            <i class="text-supplement2 text-4xl fas fa-folder mb-3"></i>
                            <span class="text-sm text-supplement1 font-medium text-center truncate w-full">docs</span>
                            <span class="text-xs text-gray-400 mt-1">Folder</span>
                        </div>
                        
                        <div class="flex flex-col items-center p-4 rounded-lg hover:bg-gray-800/50 cursor-pointer transition-colors" onclick="selectFile('README.md')">
                            <i class="text-gray-400 text-4xl fas fa-file-lines mb-3"></i>
                            <span class="text-sm text-supplement1 font-medium text-center truncate w-full">README.md</span>
                            <span class="text-xs text-gray-400 mt-1">3.1 KB</span>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleFolder(folderId) {
            // Add folder toggle functionality
        }
        
        function selectFile(fileName) {
            // Add file selection functionality
        }
    </script>

</body></html>