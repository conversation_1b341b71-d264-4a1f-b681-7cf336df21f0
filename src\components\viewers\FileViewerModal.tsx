import React, { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faTimes,
  faExpand,
  faCompress,
  faDownload,
  faShare,
  faEdit,
  faCopy,
  faExternalLinkAlt,
  faSpinner,
  faExclamationTriangle,
  faEye,
  faCode,
  faImage,
  faFileText,
  faFileArchive,
  faSearch,
  faSearchPlus,
  faSearchMinus,
  faRotateLeft,
  faRotateRight,
  faFitScreen,
  faMaximize,
  faMinimize
} from '@fortawesome/free-solid-svg-icons'
import { FileTreeNode } from '../../types'
import TextViewer from './TextViewer'
import ImageViewer from './ImageViewer'
import CodeViewer from './CodeViewer'
import ArchiveViewer from './ArchiveViewer'

interface FileViewerModalProps {
  file: FileTreeNode | null
  isOpen: boolean
  onClose: () => void
  onAction: (action: string, file: FileTreeNode) => void
  initialViewMode?: 'preview' | 'edit'
}

type ViewerType = 'text' | 'code' | 'image' | 'archive' | 'unsupported'

const FileViewerModal: React.FC<FileViewerModalProps> = ({
  file,
  isOpen,
  onClose,
  onAction,
  initialViewMode = 'preview'
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [viewMode, setViewMode] = useState<'preview' | 'edit'>(initialViewMode)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [fileContent, setFileContent] = useState<string>('')

  useEffect(() => {
    if (isOpen && file) {
      loadFileContent(file)
    }
  }, [isOpen, file])

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return

      switch (event.key) {
        case 'Escape':
          if (isFullscreen) {
            setIsFullscreen(false)
          } else {
            onClose()
          }
          break
        case 'F11':
          event.preventDefault()
          setIsFullscreen(!isFullscreen)
          break
        case 'e':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            if (getViewerType(file!) !== 'image') {
              setViewMode(viewMode === 'edit' ? 'preview' : 'edit')
            }
          }
          break
        case 's':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            handleAction('download')
          }
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, isFullscreen, viewMode, file])

  const loadFileContent = async (file: FileTreeNode) => {
    setIsLoading(true)
    setError(null)
    
    try {
      // Simulate loading file content
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const extension = file.name.split('.').pop()?.toLowerCase() || ''
      
      // Mock content based on file type
      if (['txt', 'md', 'log'].includes(extension)) {
        setFileContent(`# ${file.name}\n\nThis is a sample text file content.\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n\n## Features\n- Text editing\n- Syntax highlighting\n- Search functionality\n- Line numbers`)
      } else if (['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'html', 'css'].includes(extension)) {
        setFileContent(`// ${file.name}\n\nfunction exampleFunction() {\n  const message = "Hello, World!";\n  console.log(message);\n  \n  return {\n    success: true,\n    data: message,\n    timestamp: new Date().toISOString()\n  };\n}\n\n// Export the function\nexport default exampleFunction;\n\n// Additional code...\nclass ExampleClass {\n  constructor(name) {\n    this.name = name;\n  }\n  \n  greet() {\n    return \`Hello, \${this.name}!\`;\n  }\n}`)
      } else if (['json', 'xml', 'yaml', 'yml'].includes(extension)) {
        setFileContent(`{\n  "name": "${file.name}",\n  "version": "1.0.0",\n  "description": "Sample JSON file",\n  "main": "index.js",\n  "scripts": {\n    "start": "node index.js",\n    "test": "jest",\n    "build": "webpack"\n  },\n  "dependencies": {\n    "react": "^18.0.0",\n    "typescript": "^4.9.0"\n  },\n  "devDependencies": {\n    "jest": "^29.0.0",\n    "webpack": "^5.0.0"\n  }\n}`)
      } else {
        setFileContent('Binary file content or unsupported format')
      }
    } catch (err) {
      setError('Failed to load file content')
    } finally {
      setIsLoading(false)
    }
  }

  const getViewerType = (file: FileTreeNode): ViewerType => {
    const extension = file.name.split('.').pop()?.toLowerCase() || ''
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico'].includes(extension)) {
      return 'image'
    }
    
    if (['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'scala', 'html', 'css', 'scss', 'less', 'json', 'xml', 'yaml', 'yml'].includes(extension)) {
      return 'code'
    }
    
    if (['txt', 'md', 'log', 'rtf'].includes(extension)) {
      return 'text'
    }
    
    if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].includes(extension)) {
      return 'archive'
    }
    
    return 'unsupported'
  }

  const handleAction = (action: string) => {
    if (!file) return
    onAction(action, file)
  }

  const getViewerIcon = (type: ViewerType) => {
    switch (type) {
      case 'text': return faFileText
      case 'code': return faCode
      case 'image': return faImage
      case 'archive': return faFileArchive
      default: return faEye
    }
  }

  const canEdit = (type: ViewerType) => {
    return type === 'text' || type === 'code'
  }

  const renderViewer = () => {
    if (!file) return null
    
    const viewerType = getViewerType(file)
    
    if (isLoading) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <FontAwesomeIcon icon={faSpinner} className="w-8 h-8 text-primary animate-spin mb-4" />
            <div className="text-neutral-300">Loading file...</div>
          </div>
        </div>
      )
    }
    
    if (error) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <FontAwesomeIcon icon={faExclamationTriangle} className="w-8 h-8 text-red-400 mb-4" />
            <div className="text-red-300 mb-2">Failed to load file</div>
            <div className="text-neutral-400 text-sm">{error}</div>
          </div>
        </div>
      )
    }
    
    switch (viewerType) {
      case 'text':
        return (
          <TextViewer
            content={fileContent}
            filename={file.name}
            viewMode={viewMode}
            onContentChange={setFileContent}
            className="flex-1"
          />
        )
      
      case 'code':
        return (
          <CodeViewer
            content={fileContent}
            filename={file.name}
            viewMode={viewMode}
            onContentChange={setFileContent}
            className="flex-1"
          />
        )
      
      case 'image':
        return (
          <ImageViewer
            file={file}
            className="flex-1"
          />
        )
      
      case 'archive':
        return (
          <ArchiveViewer
            file={file}
            className="flex-1"
          />
        )
      
      default:
        return (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <FontAwesomeIcon icon={faExclamationTriangle} className="w-8 h-8 text-yellow-400 mb-4" />
              <div className="text-neutral-300 mb-2">Unsupported file type</div>
              <div className="text-neutral-400 text-sm">This file type cannot be previewed</div>
              <button
                onClick={() => handleAction('open-with-system')}
                className="mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
              >
                <FontAwesomeIcon icon={faExternalLinkAlt} className="w-4 h-4 mr-2" />
                Open with System Default
              </button>
            </div>
          </div>
        )
    }
  }

  if (!isOpen || !file) return null

  const viewerType = getViewerType(file)
  const canEditFile = canEdit(viewerType)

  return (
    <div className={`
      fixed inset-0 z-50 flex items-center justify-center
      ${isFullscreen ? 'p-0' : 'p-4'}
    `}>
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/80 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className={`
        relative bg-neutral-900 border border-neutral-700 shadow-2xl flex flex-col
        ${isFullscreen 
          ? 'w-full h-full rounded-none' 
          : 'w-full max-w-6xl h-full max-h-[90vh] rounded-xl'
        }
      `}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-neutral-700 bg-neutral-800/50">
          <div className="flex items-center gap-3">
            <FontAwesomeIcon 
              icon={getViewerIcon(viewerType)} 
              className="w-5 h-5 text-primary" 
            />
            <div>
              <div className="text-white font-medium">{file.name}</div>
              <div className="text-sm text-neutral-400">{file.path}</div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {/* View mode toggle */}
            {canEditFile && (
              <div className="flex bg-neutral-800 rounded-lg p-1 mr-2">
                <button
                  onClick={() => setViewMode('preview')}
                  className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                    viewMode === 'preview'
                      ? 'bg-neutral-700 text-white'
                      : 'text-neutral-400 hover:text-white'
                  }`}
                >
                  <FontAwesomeIcon icon={faEye} className="w-3 h-3 mr-2" />
                  Preview
                </button>
                <button
                  onClick={() => setViewMode('edit')}
                  className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                    viewMode === 'edit'
                      ? 'bg-neutral-700 text-white'
                      : 'text-neutral-400 hover:text-white'
                  }`}
                >
                  <FontAwesomeIcon icon={faEdit} className="w-3 h-3 mr-2" />
                  Edit
                </button>
              </div>
            )}
            
            {/* Action buttons */}
            <button
              onClick={() => handleAction('copy-content')}
              className="p-2 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded-lg transition-colors"
              title="Copy content"
            >
              <FontAwesomeIcon icon={faCopy} className="w-4 h-4" />
            </button>
            
            <button
              onClick={() => handleAction('download')}
              className="p-2 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded-lg transition-colors"
              title="Download file"
            >
              <FontAwesomeIcon icon={faDownload} className="w-4 h-4" />
            </button>
            
            <button
              onClick={() => handleAction('share')}
              className="p-2 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded-lg transition-colors"
              title="Share file"
            >
              <FontAwesomeIcon icon={faShare} className="w-4 h-4" />
            </button>
            
            <button
              onClick={() => handleAction('open-with-system')}
              className="p-2 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded-lg transition-colors"
              title="Open with system default"
            >
              <FontAwesomeIcon icon={faExternalLinkAlt} className="w-4 h-4" />
            </button>
            
            <div className="w-px h-6 bg-neutral-600 mx-1" />
            
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="p-2 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded-lg transition-colors"
              title={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
            >
              <FontAwesomeIcon icon={isFullscreen ? faCompress : faExpand} className="w-4 h-4" />
            </button>
            
            <button
              onClick={onClose}
              className="p-2 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded-lg transition-colors"
              title="Close"
            >
              <FontAwesomeIcon icon={faTimes} className="w-4 h-4" />
            </button>
          </div>
        </div>
        
        {/* Content */}
        {renderViewer()}
        
        {/* Footer */}
        <div className="flex items-center justify-between p-3 border-t border-neutral-700 bg-neutral-800/50 text-sm text-neutral-400">
          <div className="flex items-center gap-4">
            <span>Type: {viewerType}</span>
            <span>Size: {Math.floor(Math.random() * 1000) + 100} KB</span>
            {viewMode === 'edit' && (
              <span className="text-yellow-400">• Modified</span>
            )}
          </div>
          
          <div className="flex items-center gap-4">
            <span>Ctrl+E: Toggle Edit</span>
            <span>F11: Fullscreen</span>
            <span>Esc: Close</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FileViewerModal
