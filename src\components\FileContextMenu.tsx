import React, { useState, useEffect, useRef } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faFolderOpen,
  faEye,
  faEdit,
  faCopy,
  faShare,
  faTrash,
  faInfoCircle,
  faExternalLinkAlt,
  faCode,
  faImage,
  faFileText,
  faFilePdf,
  faFileWord,
  faFileExcel,
  faFilePowerpoint,
  faFileArchive,
  faPlay,
  faMusic,
  faChevronRight,
  faDesktop,
  faCloud,
  faDownload
} from '@fortawesome/free-solid-svg-icons'
import { FileTreeNode } from '../types'

interface FileContextMenuProps {
  file: FileTreeNode
  position: { x: number; y: number }
  onClose: () => void
  onAction: (action: string, file: FileTreeNode) => void
  isVisible: boolean
}

interface MenuAction {
  id: string
  label: string
  icon: any
  shortcut?: string
  disabled?: boolean
  danger?: boolean
  submenu?: MenuAction[]
}

const FileContextMenu: React.FC<FileContextMenuProps> = ({
  file,
  position,
  onClose,
  onAction,
  isVisible
}) => {
  const menuRef = useRef<HTMLDivElement>(null)
  const [submenuOpen, setSubmenuOpen] = useState<string | null>(null)

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isVisible, onClose])

  // Close menu on escape key
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (isVisible) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isVisible, onClose])

  if (!isVisible) return null

  const getFileTypeActions = (file: FileTreeNode): MenuAction[] => {
    const fileExtension = file.name.split('.').pop()?.toLowerCase() || ''
    const isTextFile = ['txt', 'md', 'log', 'json', 'xml', 'csv', 'yaml', 'yml'].includes(fileExtension)
    const isCodeFile = ['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'scala', 'html', 'css', 'scss', 'less'].includes(fileExtension)
    const isImageFile = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico'].includes(fileExtension)
    const isPdfFile = fileExtension === 'pdf'
    const isOfficeFile = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileExtension)
    const isArchiveFile = ['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].includes(fileExtension)
    const isVideoFile = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].includes(fileExtension)
    const isAudioFile = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma'].includes(fileExtension)

    const actions: MenuAction[] = []

    // Primary action based on file type
    if (isTextFile || isCodeFile) {
      actions.push({
        id: 'edit-in-chatlo',
        label: 'Edit in ChatLo',
        icon: faEdit,
        shortcut: 'Enter'
      })
    } else if (isImageFile) {
      actions.push({
        id: 'preview-in-chatlo',
        label: 'Preview in ChatLo',
        icon: faEye,
        shortcut: 'Enter'
      })
    } else {
      actions.push({
        id: 'open-with-system',
        label: 'Open with System Default',
        icon: faExternalLinkAlt,
        shortcut: 'Enter'
      })
    }

    // Secondary actions
    if (isTextFile || isCodeFile || isImageFile || isPdfFile) {
      actions.push({
        id: 'preview-in-chatlo',
        label: 'Quick Preview',
        icon: faEye,
        shortcut: 'Space'
      })
    }

    if (isArchiveFile) {
      actions.push({
        id: 'explore-archive',
        label: 'Explore Archive',
        icon: faFolderOpen
      })
    }

    // Always available system action
    actions.push({
      id: 'open-with-system',
      label: 'Open with System Default',
      icon: faDesktop,
      shortcut: 'Ctrl+O'
    })

    return actions
  }

  const getOpenWithSubmenu = (): MenuAction[] => {
    const fileExtension = file.name.split('.').pop()?.toLowerCase() || ''
    const submenu: MenuAction[] = []

    // System default
    submenu.push({
      id: 'open-system-default',
      label: 'System Default',
      icon: faDesktop
    })

    // Code files - suggest VS Code, Notepad++
    if (['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'scala', 'html', 'css', 'scss', 'less'].includes(fileExtension)) {
      submenu.push({
        id: 'open-vscode',
        label: 'Visual Studio Code',
        icon: faCode
      })
      submenu.push({
        id: 'open-notepad-plus',
        label: 'Notepad++',
        icon: faEdit
      })
    }

    // Image files - suggest image viewers
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(fileExtension)) {
      submenu.push({
        id: 'open-image-viewer',
        label: 'System Image Viewer',
        icon: faImage
      })
    }

    // PDF files
    if (fileExtension === 'pdf') {
      submenu.push({
        id: 'open-pdf-reader',
        label: 'PDF Reader',
        icon: faFilePdf
      })
    }

    return submenu
  }

  const getSendToSubmenu = (): MenuAction[] => {
    return [
      {
        id: 'send-to-dropbox',
        label: 'Dropbox',
        icon: faCloud
      },
      {
        id: 'send-to-google-drive',
        label: 'Google Drive',
        icon: faCloud
      },
      {
        id: 'send-to-onedrive',
        label: 'OneDrive',
        icon: faCloud
      },
      {
        id: 'send-to-email',
        label: 'Email as Attachment',
        icon: faShare
      }
    ]
  }

  const mainActions: MenuAction[] = [
    ...getFileTypeActions(file),
    { id: 'separator-1', label: '', icon: null }, // Separator
    {
      id: 'open-with',
      label: 'Open with...',
      icon: faExternalLinkAlt,
      submenu: getOpenWithSubmenu()
    },
    { id: 'separator-2', label: '', icon: null }, // Separator
    {
      id: 'copy-path',
      label: 'Copy File Path',
      icon: faCopy,
      shortcut: 'Ctrl+Shift+C'
    },
    {
      id: 'show-in-folder',
      label: 'Show in Folder',
      icon: faFolderOpen,
      shortcut: 'Ctrl+Shift+R'
    },
    {
      id: 'send-to',
      label: 'Send to...',
      icon: faShare,
      submenu: getSendToSubmenu()
    },
    { id: 'separator-3', label: '', icon: null }, // Separator
    {
      id: 'download-copy',
      label: 'Download Copy',
      icon: faDownload
    },
    {
      id: 'properties',
      label: 'Properties',
      icon: faInfoCircle,
      shortcut: 'Alt+Enter'
    },
    { id: 'separator-4', label: '', icon: null }, // Separator
    {
      id: 'delete',
      label: 'Move to Trash',
      icon: faTrash,
      shortcut: 'Delete',
      danger: true
    }
  ]

  const handleActionClick = (action: MenuAction) => {
    if (action.submenu) {
      setSubmenuOpen(submenuOpen === action.id ? null : action.id)
      return
    }

    if (action.id.startsWith('separator')) return

    onAction(action.id, file)
    onClose()
  }

  const renderMenuItem = (action: MenuAction, isSubmenu = false) => {
    if (action.id.startsWith('separator')) {
      return (
        <div key={action.id} className="h-px bg-neutral-700 my-1" />
      )
    }

    const hasSubmenu = action.submenu && action.submenu.length > 0
    const isSubmenuOpen = submenuOpen === action.id

    return (
      <div key={action.id} className="relative">
        <button
          className={`
            w-full px-3 py-2 text-left text-sm flex items-center gap-3 transition-colors
            ${action.danger 
              ? 'text-red-400 hover:bg-red-500/10 hover:text-red-300' 
              : 'text-neutral-300 hover:bg-neutral-700 hover:text-white'
            }
            ${action.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
            ${isSubmenu ? 'pl-6' : ''}
          `}
          onClick={() => handleActionClick(action)}
          disabled={action.disabled}
        >
          <FontAwesomeIcon 
            icon={action.icon} 
            className={`w-4 h-4 ${action.danger ? 'text-red-400' : 'text-neutral-400'}`} 
          />
          <span className="flex-1">{action.label}</span>
          {action.shortcut && (
            <span className="text-xs text-neutral-500">{action.shortcut}</span>
          )}
          {hasSubmenu && (
            <FontAwesomeIcon 
              icon={faChevronRight} 
              className="w-3 h-3 text-neutral-500" 
            />
          )}
        </button>

        {/* Submenu */}
        {hasSubmenu && isSubmenuOpen && (
          <div className="absolute left-full top-0 ml-1 min-w-48 bg-neutral-800 border border-neutral-700 rounded-lg shadow-xl z-50">
            {action.submenu!.map(subAction => renderMenuItem(subAction, true))}
          </div>
        )}
      </div>
    )
  }

  // Calculate menu position to keep it on screen
  const menuStyle: React.CSSProperties = {
    position: 'fixed',
    left: Math.min(position.x, window.innerWidth - 250), // 250px estimated menu width
    top: Math.min(position.y, window.innerHeight - 400), // 400px estimated menu height
    zIndex: 1000
  }

  return (
    <div
      ref={menuRef}
      className="min-w-64 bg-neutral-800 border border-neutral-700 rounded-lg shadow-xl py-2"
      style={menuStyle}
    >
      {/* File info header */}
      <div className="px-3 py-2 border-b border-neutral-700 mb-1">
        <div className="flex items-center gap-2">
          <FontAwesomeIcon 
            icon={file.icon} 
            className="w-4 h-4 text-primary" 
          />
          <span className="text-sm font-medium text-white truncate">
            {file.name}
          </span>
        </div>
        <div className="text-xs text-neutral-500 mt-1">
          {file.type === 'folder' ? 'Folder' : 'File'} • {file.path}
        </div>
      </div>

      {/* Menu actions */}
      {mainActions.map(action => renderMenuItem(action))}
    </div>
  )
}

export default FileContextMenu
