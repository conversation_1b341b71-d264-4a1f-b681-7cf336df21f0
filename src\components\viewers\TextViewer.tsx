import React, { useState, useRef, useEffect } from 'react'
import { Icon } from '../icons/FontAwesomeIcons'

interface TextViewerProps {
  content: string
  filename: string
  viewMode: 'preview' | 'edit'
  onContentChange: (content: string) => void
  className?: string
}

const TextViewer: React.FC<TextViewerProps> = ({
  content,
  filename,
  viewMode,
  onContentChange,
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [fontSize, setFontSize] = useState(14)
  const [wordWrap, setWordWrap] = useState(true)
  const [lineNumbers, setLineNumbers] = useState(true)
  const [isModified, setIsModified] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const preRef = useRef<HTMLPreElement>(null)

  useEffect(() => {
    if (viewMode === 'edit' && textareaRef.current) {
      textareaRef.current.focus()
    }
  }, [viewMode])

  const handleContentChange = (newContent: string) => {
    onContentChange(newContent)
    setIsModified(true)
  }

  const handleSave = async () => {
    setIsSaving(true)
    // Simulate save operation
    await new Promise(resolve => setTimeout(resolve, 1000))
    setIsSaving(false)
    setIsModified(false)
  }

  const handleSearch = (term: string) => {
    setSearchTerm(term)
    // In a real implementation, this would highlight search results
  }

  const adjustFontSize = (delta: number) => {
    setFontSize(prev => Math.max(10, Math.min(24, prev + delta)))
  }

  const getLineCount = () => {
    return content.split('\n').length
  }

  const renderLineNumbers = () => {
    if (!lineNumbers) return null
    
    const lines = content.split('\n')
    return (
      <div 
        className="flex-shrink-0 text-right pr-3 py-3 text-neutral-500 select-none border-r border-neutral-700"
        style={{ fontSize: `${fontSize}px`, lineHeight: '1.5' }}
      >
        {lines.map((_, index) => (
          <div key={index} className="leading-6">
            {index + 1}
          </div>
        ))}
      </div>
    )
  }

  const renderPreview = () => {
    const isMarkdown = filename.toLowerCase().endsWith('.md')
    
    return (
      <div className="flex-1 flex">
        {renderLineNumbers()}
        <div className="flex-1 overflow-auto">
          <pre
            ref={preRef}
            className={`
              p-3 text-neutral-300 font-mono leading-6
              ${wordWrap ? 'whitespace-pre-wrap' : 'whitespace-pre'}
            `}
            style={{ fontSize: `${fontSize}px` }}
          >
            {content}
          </pre>
        </div>
      </div>
    )
  }

  const renderEditor = () => {
    return (
      <div className="flex-1 flex">
        {renderLineNumbers()}
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={content}
            onChange={(e) => handleContentChange(e.target.value)}
            className={`
              w-full h-full p-3 bg-transparent text-neutral-300 font-mono leading-6 resize-none outline-none
              ${wordWrap ? 'whitespace-pre-wrap' : 'whitespace-pre'}
            `}
            style={{ fontSize: `${fontSize}px` }}
            spellCheck={false}
          />
        </div>
      </div>
    )
  }

  return (
    <div className={`flex flex-col bg-neutral-900 ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-3 border-b border-neutral-700 bg-neutral-800/50">
        <div className="flex items-center gap-3">
          {/* Search */}
          <div className="relative">
            <Icon
              name="search"
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-500"
              size="xs"
            />
            <input
              type="text"
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-9 pr-3 py-1.5 bg-neutral-800 border border-neutral-600 rounded-lg text-sm text-neutral-300 placeholder-neutral-500 focus:outline-none focus:border-primary"
            />
          </div>

          {/* Font size controls */}
          <div className="flex items-center gap-1">
            <button
              onClick={() => adjustFontSize(-1)}
              className="p-1.5 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded transition-colors"
              title="Decrease font size"
            >
              <Icon name="minus" size="xs" />
            </button>
            <span className="text-xs text-neutral-400 min-w-8 text-center">{fontSize}px</span>
            <button
              onClick={() => adjustFontSize(1)}
              className="p-1.5 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded transition-colors"
              title="Increase font size"
            >
              <Icon name="plus" size="xs" />
            </button>
          </div>

          <div className="w-px h-4 bg-neutral-600" />

          {/* Text options */}
          <button
            onClick={() => setWordWrap(!wordWrap)}
            className={`p-1.5 rounded transition-colors ${
              wordWrap
                ? 'text-primary bg-primary/10'
                : 'text-neutral-400 hover:text-white hover:bg-neutral-700'
            }`}
            title="Toggle word wrap"
          >
            <Icon name="alignLeft" size="xs" />
          </button>

          <button
            onClick={() => setLineNumbers(!lineNumbers)}
            className={`p-1.5 rounded transition-colors ${
              lineNumbers
                ? 'text-primary bg-primary/10'
                : 'text-neutral-400 hover:text-white hover:bg-neutral-700'
            }`}
            title="Toggle line numbers"
          >
            <Icon name="font" size="xs" />
          </button>
        </div>

        <div className="flex items-center gap-3">
          {/* File info */}
          <div className="text-sm text-neutral-400">
            {getLineCount()} lines • {content.length} characters
          </div>

          {/* Edit mode controls */}
          {viewMode === 'edit' && (
            <>
              <div className="w-px h-4 bg-neutral-600" />
              
              {isModified && (
                <span className="text-xs text-yellow-400">Modified</span>
              )}
              
              <button
                onClick={handleSave}
                disabled={!isModified || isSaving}
                className={`
                  px-3 py-1.5 text-sm rounded-lg transition-colors flex items-center gap-2
                  ${isModified && !isSaving
                    ? 'bg-primary text-white hover:bg-primary/90'
                    : 'bg-neutral-700 text-neutral-400 cursor-not-allowed'
                  }
                `}
              >
                {isSaving ? (
                  <Icon name="spinner" size="xs" spin />
                ) : (
                  <Icon name="save" size="xs" />
                )}
                Save
              </button>
            </>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {viewMode === 'preview' ? renderPreview() : renderEditor()}
      </div>

      {/* Status bar */}
      <div className="flex items-center justify-between px-3 py-2 border-t border-neutral-700 bg-neutral-800/30 text-xs text-neutral-500">
        <div className="flex items-center gap-4">
          <span>Text File</span>
          <span>UTF-8</span>
          <span>LF</span>
        </div>
        
        <div className="flex items-center gap-4">
          {viewMode === 'edit' && (
            <>
              <span>Ln 1, Col 1</span>
              <span>Sel 0</span>
            </>
          )}
          <span>{viewMode === 'edit' ? 'Edit Mode' : 'Preview Mode'}</span>
        </div>
      </div>
    </div>
  )
}

export default TextViewer
