import React from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faPlay,
  faEye,
  faEdit,
  faShare,
  faTrash,
  faDownload,
  faCopy,
  faFolderOpen,
  faInfoCircle,
  faExternalLinkAlt,
  faCode,
  faImage,
  faFileText,
  faFilePdf,
  faFileArchive,
  faDesktop,
  faSpinner
} from '@fortawesome/free-solid-svg-icons'
import { FileTreeNode } from '../types'

interface FileActionsToolbarProps {
  selectedFiles: FileTreeNode[]
  onAction: (action: string, files: FileTreeNode[]) => void
  isLoading?: boolean
  className?: string
}

interface ToolbarAction {
  id: string
  label: string
  icon: any
  shortcut?: string
  disabled?: boolean
  danger?: boolean
  primary?: boolean
  tooltip?: string
}

const FileActionsToolbar: React.FC<FileActionsToolbarProps> = ({
  selectedFiles,
  onAction,
  isLoading = false,
  className = ''
}) => {
  const hasSelection = selectedFiles.length > 0
  const singleSelection = selectedFiles.length === 1
  const multipleSelection = selectedFiles.length > 1
  const selectedFile = singleSelection ? selectedFiles[0] : null

  const getFileTypeInfo = (file: FileTreeNode) => {
    const fileExtension = file.name.split('.').pop()?.toLowerCase() || ''
    return {
      isTextFile: ['txt', 'md', 'log', 'json', 'xml', 'csv', 'yaml', 'yml'].includes(fileExtension),
      isCodeFile: ['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'scala', 'html', 'css', 'scss', 'less'].includes(fileExtension),
      isImageFile: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico'].includes(fileExtension),
      isPdfFile: fileExtension === 'pdf',
      isOfficeFile: ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileExtension),
      isArchiveFile: ['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].includes(fileExtension),
      isVideoFile: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].includes(fileExtension),
      isAudioFile: ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma'].includes(fileExtension),
      extension: fileExtension
    }
  }

  const getPrimaryActions = (): ToolbarAction[] => {
    if (!hasSelection) return []

    const actions: ToolbarAction[] = []

    if (singleSelection && selectedFile) {
      const fileInfo = getFileTypeInfo(selectedFile)

      // Primary action based on file type
      if (fileInfo.isTextFile || fileInfo.isCodeFile) {
        actions.push({
          id: 'edit-in-chatlo',
          label: 'Edit',
          icon: faEdit,
          shortcut: 'Enter',
          primary: true,
          tooltip: 'Edit file in ChatLo editor'
        })
      } else if (fileInfo.isImageFile) {
        actions.push({
          id: 'preview-in-chatlo',
          label: 'Preview',
          icon: faEye,
          shortcut: 'Enter',
          primary: true,
          tooltip: 'Preview image in ChatLo'
        })
      } else {
        actions.push({
          id: 'open-with-system',
          label: 'Open',
          icon: faPlay,
          shortcut: 'Enter',
          primary: true,
          tooltip: 'Open with system default application'
        })
      }

      // Quick preview for supported files
      if (fileInfo.isTextFile || fileInfo.isCodeFile || fileInfo.isImageFile || fileInfo.isPdfFile) {
        actions.push({
          id: 'quick-preview',
          label: 'Quick Look',
          icon: faEye,
          shortcut: 'Space',
          tooltip: 'Quick preview without opening'
        })
      }

      // Archive exploration
      if (fileInfo.isArchiveFile) {
        actions.push({
          id: 'explore-archive',
          label: 'Explore',
          icon: faFolderOpen,
          tooltip: 'Explore archive contents'
        })
      }
    } else if (multipleSelection) {
      // Multi-selection actions
      actions.push({
        id: 'open-all-system',
        label: 'Open All',
        icon: faPlay,
        primary: true,
        tooltip: 'Open all selected files with system defaults'
      })
    }

    return actions
  }

  const getSecondaryActions = (): ToolbarAction[] => {
    if (!hasSelection) return []

    const actions: ToolbarAction[] = []

    // Always available actions
    actions.push({
      id: 'open-with-system',
      label: 'System Default',
      icon: faDesktop,
      shortcut: 'Ctrl+O',
      tooltip: 'Open with system default application'
    })

    actions.push({
      id: 'show-in-folder',
      label: 'Show in Folder',
      icon: faFolderOpen,
      shortcut: 'Ctrl+Shift+R',
      tooltip: 'Show file location in system file explorer'
    })

    actions.push({
      id: 'copy-path',
      label: 'Copy Path',
      icon: faCopy,
      shortcut: 'Ctrl+Shift+C',
      tooltip: 'Copy file path to clipboard'
    })

    actions.push({
      id: 'download-copy',
      label: 'Download',
      icon: faDownload,
      tooltip: 'Download a copy of the file'
    })

    actions.push({
      id: 'share',
      label: 'Share',
      icon: faShare,
      tooltip: 'Share file to external services'
    })

    if (singleSelection) {
      actions.push({
        id: 'properties',
        label: 'Properties',
        icon: faInfoCircle,
        shortcut: 'Alt+Enter',
        tooltip: 'View file properties and metadata'
      })
    }

    return actions
  }

  const getDangerActions = (): ToolbarAction[] => {
    if (!hasSelection) return []

    return [
      {
        id: 'delete',
        label: multipleSelection ? `Delete ${selectedFiles.length} files` : 'Delete',
        icon: faTrash,
        shortcut: 'Delete',
        danger: true,
        tooltip: multipleSelection 
          ? `Move ${selectedFiles.length} selected files to trash`
          : 'Move file to trash'
      }
    ]
  }

  const handleActionClick = (action: ToolbarAction) => {
    if (action.disabled || isLoading) return
    onAction(action.id, selectedFiles)
  }

  const renderActionButton = (action: ToolbarAction, size: 'small' | 'medium' = 'medium') => {
    const isSmall = size === 'small'
    
    return (
      <button
        key={action.id}
        onClick={() => handleActionClick(action)}
        disabled={action.disabled || isLoading}
        className={`
          ${isSmall ? 'px-2 py-1.5' : 'px-3 py-2'} 
          rounded-lg border transition-all duration-200 flex items-center gap-2
          ${action.primary
            ? 'bg-primary text-white border-primary hover:bg-primary/90 shadow-sm'
            : action.danger
            ? 'bg-transparent text-red-400 border-red-400/30 hover:bg-red-500/10 hover:border-red-400'
            : 'bg-transparent text-neutral-300 border-neutral-600 hover:bg-neutral-700 hover:border-neutral-500'
          }
          ${action.disabled || isLoading 
            ? 'opacity-50 cursor-not-allowed' 
            : 'cursor-pointer hover:shadow-sm'
          }
          ${isSmall ? 'text-xs' : 'text-sm'}
        `}
        title={action.tooltip}
      >
        {isLoading && action.primary ? (
          <FontAwesomeIcon icon={faSpinner} className={`${isSmall ? 'w-3 h-3' : 'w-4 h-4'} animate-spin`} />
        ) : (
          <FontAwesomeIcon 
            icon={action.icon} 
            className={`${isSmall ? 'w-3 h-3' : 'w-4 h-4'}`} 
          />
        )}
        <span className={isSmall ? 'hidden sm:inline' : ''}>{action.label}</span>
        {action.shortcut && !isSmall && (
          <span className="text-xs opacity-60 ml-1">({action.shortcut})</span>
        )}
      </button>
    )
  }

  const primaryActions = getPrimaryActions()
  const secondaryActions = getSecondaryActions()
  const dangerActions = getDangerActions()

  if (!hasSelection) {
    return (
      <div className={`flex items-center justify-between p-3 bg-neutral-800/50 border-b border-neutral-700 ${className}`}>
        <div className="text-sm text-neutral-500">
          Select files to see available actions
        </div>
        <div className="text-xs text-neutral-600">
          Right-click for context menu • Double-click to open
        </div>
      </div>
    )
  }

  return (
    <div className={`flex items-center justify-between p-3 bg-neutral-800/50 border-b border-neutral-700 ${className}`}>
      {/* Left side - Primary actions */}
      <div className="flex items-center gap-2">
        {primaryActions.map(action => renderActionButton(action))}
        
        {primaryActions.length > 0 && secondaryActions.length > 0 && (
          <div className="w-px h-6 bg-neutral-600 mx-2" />
        )}
        
        {secondaryActions.slice(0, 3).map(action => renderActionButton(action, 'small'))}
        
        {secondaryActions.length > 3 && (
          <button
            className="px-2 py-1.5 text-xs text-neutral-400 hover:text-neutral-300 transition-colors"
            title="More actions"
          >
            +{secondaryActions.length - 3} more
          </button>
        )}
      </div>

      {/* Right side - Selection info and danger actions */}
      <div className="flex items-center gap-3">
        <div className="text-sm text-neutral-400">
          {multipleSelection 
            ? `${selectedFiles.length} files selected`
            : selectedFile?.name
          }
        </div>
        
        {dangerActions.length > 0 && (
          <>
            <div className="w-px h-6 bg-neutral-600" />
            {dangerActions.map(action => renderActionButton(action, 'small'))}
          </>
        )}
      </div>
    </div>
  )
}

export default FileActionsToolbar
