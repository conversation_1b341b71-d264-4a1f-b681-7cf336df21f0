import React, { useState, useRef, useEffect } from 'react'
import { Icon } from '../icons/FontAwesomeIcons'

interface CodeViewerProps {
  content: string
  filename: string
  viewMode: 'preview' | 'edit'
  onContentChange: (content: string) => void
  className?: string
}

const CodeViewer: React.FC<CodeViewerProps> = ({
  content,
  filename,
  viewMode,
  onContentChange,
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [fontSize, setFontSize] = useState(14)
  const [wordWrap, setWordWrap] = useState(false)
  const [lineNumbers, setLineNumbers] = useState(true)
  const [syntaxHighlight, setSyntaxHighlight] = useState(true)
  const [theme, setTheme] = useState('dark')
  const [isModified, setIsModified] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [isMinimapVisible, setIsMinimapVisible] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const preRef = useRef<HTMLPreElement>(null)

  const getLanguage = () => {
    const extension = filename.split('.').pop()?.toLowerCase() || ''
    const languageMap: Record<string, string> = {
      js: 'javascript',
      jsx: 'javascript',
      ts: 'typescript',
      tsx: 'typescript',
      py: 'python',
      java: 'java',
      cpp: 'cpp',
      c: 'c',
      cs: 'csharp',
      php: 'php',
      rb: 'ruby',
      go: 'go',
      rs: 'rust',
      swift: 'swift',
      kt: 'kotlin',
      html: 'html',
      css: 'css',
      scss: 'scss',
      json: 'json',
      xml: 'xml',
      yaml: 'yaml',
      yml: 'yaml'
    }
    return languageMap[extension] || 'text'
  }

  const handleContentChange = (newContent: string) => {
    onContentChange(newContent)
    setIsModified(true)
  }

  const handleSave = async () => {
    setIsSaving(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setIsSaving(false)
    setIsModified(false)
  }

  const handleRun = () => {
    // Simulate code execution
    console.log('Running code...')
  }

  const handleCopyCode = () => {
    navigator.clipboard.writeText(content)
  }

  const adjustFontSize = (delta: number) => {
    setFontSize(prev => Math.max(10, Math.min(24, prev + delta)))
  }

  const getLineCount = () => {
    return content.split('\n').length
  }

  const renderLineNumbers = () => {
    if (!lineNumbers) return null
    
    const lines = content.split('\n')
    return (
      <div 
        className="flex-shrink-0 text-right pr-3 py-3 text-neutral-500 select-none border-r border-neutral-700"
        style={{ fontSize: `${fontSize}px`, lineHeight: '1.5' }}
      >
        {lines.map((_, index) => (
          <div key={index} className="leading-6 hover:bg-neutral-800/50 px-1">
            {index + 1}
          </div>
        ))}
      </div>
    )
  }

  const renderSyntaxHighlightedCode = (code: string) => {
    // Simple syntax highlighting simulation
    // In a real implementation, you'd use a library like Prism.js or highlight.js
    const language = getLanguage()
    
    let highlightedCode = code
    
    if (language === 'javascript' || language === 'typescript') {
      highlightedCode = code
        .replace(/\b(function|const|let|var|if|else|for|while|return|import|export|class|extends)\b/g, '<span class="text-purple-400">$1</span>')
        .replace(/\b(true|false|null|undefined)\b/g, '<span class="text-orange-400">$1</span>')
        .replace(/"([^"]*)"/g, '<span class="text-green-400">"$1"</span>')
        .replace(/'([^']*)'/g, '<span class="text-green-400">\'$1\'</span>')
        .replace(/\/\/.*$/gm, '<span class="text-neutral-500">$&</span>')
        .replace(/\/\*[\s\S]*?\*\//g, '<span class="text-neutral-500">$&</span>')
    } else if (language === 'python') {
      highlightedCode = code
        .replace(/\b(def|class|if|elif|else|for|while|return|import|from|as|try|except|finally|with|lambda)\b/g, '<span class="text-purple-400">$1</span>')
        .replace(/\b(True|False|None)\b/g, '<span class="text-orange-400">$1</span>')
        .replace(/"([^"]*)"/g, '<span class="text-green-400">"$1"</span>')
        .replace(/'([^']*)'/g, '<span class="text-green-400">\'$1\'</span>')
        .replace(/#.*$/gm, '<span class="text-neutral-500">$&</span>')
    }
    
    return { __html: highlightedCode }
  }

  const renderPreview = () => {
    return (
      <div className="flex-1 flex">
        {renderLineNumbers()}
        <div className="flex-1 overflow-auto">
          {syntaxHighlight ? (
            <pre
              ref={preRef}
              className={`
                p-3 text-neutral-300 font-mono leading-6
                ${wordWrap ? 'whitespace-pre-wrap' : 'whitespace-pre'}
              `}
              style={{ fontSize: `${fontSize}px` }}
              dangerouslySetInnerHTML={renderSyntaxHighlightedCode(content)}
            />
          ) : (
            <pre
              ref={preRef}
              className={`
                p-3 text-neutral-300 font-mono leading-6
                ${wordWrap ? 'whitespace-pre-wrap' : 'whitespace-pre'}
              `}
              style={{ fontSize: `${fontSize}px` }}
            >
              {content}
            </pre>
          )}
        </div>
        
        {/* Minimap */}
        {isMinimapVisible && (
          <div className="w-24 bg-neutral-800/50 border-l border-neutral-700 overflow-hidden">
            <div className="p-2 text-xs text-neutral-500 border-b border-neutral-700">
              Minimap
            </div>
            <div className="p-1">
              <pre className="text-xs text-neutral-600 leading-tight overflow-hidden">
                {content.split('\n').map((line, index) => (
                  <div key={index} className="truncate hover:bg-neutral-700/50">
                    {line || ' '}
                  </div>
                ))}
              </pre>
            </div>
          </div>
        )}
      </div>
    )
  }

  const renderEditor = () => {
    return (
      <div className="flex-1 flex">
        {renderLineNumbers()}
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={content}
            onChange={(e) => handleContentChange(e.target.value)}
            className={`
              w-full h-full p-3 bg-transparent text-neutral-300 font-mono leading-6 resize-none outline-none
              ${wordWrap ? 'whitespace-pre-wrap' : 'whitespace-pre'}
            `}
            style={{ fontSize: `${fontSize}px` }}
            spellCheck={false}
          />
        </div>
        
        {/* Minimap for editor */}
        {isMinimapVisible && (
          <div className="w-24 bg-neutral-800/50 border-l border-neutral-700 overflow-hidden">
            <div className="p-2 text-xs text-neutral-500 border-b border-neutral-700">
              Minimap
            </div>
            <div className="p-1">
              <pre className="text-xs text-neutral-600 leading-tight overflow-hidden">
                {content.split('\n').map((line, index) => (
                  <div key={index} className="truncate hover:bg-neutral-700/50">
                    {line || ' '}
                  </div>
                ))}
              </pre>
            </div>
          </div>
        )}
      </div>
    )
  }

  const isExecutable = () => {
    const language = getLanguage()
    return ['javascript', 'python', 'java', 'go', 'rust'].includes(language)
  }

  return (
    <div className={`flex flex-col bg-neutral-900 ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-3 border-b border-neutral-700 bg-neutral-800/50">
        <div className="flex items-center gap-3">
          {/* Language indicator */}
          <div className="flex items-center gap-2 px-2 py-1 bg-neutral-800 rounded text-sm">
            <Icon name="code" className="text-primary" size="xs" />
            <span className="text-neutral-300 capitalize">{getLanguage()}</span>
          </div>

          {/* Search */}
          <div className="relative">
            <Icon
              name="search"
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-500"
              size="xs"
            />
            <input
              type="text"
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9 pr-3 py-1.5 bg-neutral-800 border border-neutral-600 rounded-lg text-sm text-neutral-300 placeholder-neutral-500 focus:outline-none focus:border-primary"
            />
          </div>

          {/* Font size controls */}
          <div className="flex items-center gap-1">
            <button
              onClick={() => adjustFontSize(-1)}
              className="p-1.5 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded transition-colors"
              title="Decrease font size"
            >
              <Icon name="minus" size="xs" />
            </button>
            <span className="text-xs text-neutral-400 min-w-8 text-center">{fontSize}px</span>
            <button
              onClick={() => adjustFontSize(1)}
              className="p-1.5 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded transition-colors"
              title="Increase font size"
            >
              <Icon name="plus" size="xs" />
            </button>
          </div>

          <div className="w-px h-4 bg-neutral-600" />

          {/* Code options */}
          <button
            onClick={() => setWordWrap(!wordWrap)}
            className={`p-1.5 rounded transition-colors ${
              wordWrap
                ? 'text-primary bg-primary/10'
                : 'text-neutral-400 hover:text-white hover:bg-neutral-700'
            }`}
            title="Toggle word wrap"
          >
            <Icon name="alignLeft" size="xs" />
          </button>

          <button
            onClick={() => setLineNumbers(!lineNumbers)}
            className={`p-1.5 rounded transition-colors ${
              lineNumbers
                ? 'text-primary bg-primary/10'
                : 'text-neutral-400 hover:text-white hover:bg-neutral-700'
            }`}
            title="Toggle line numbers"
          >
            <Icon name="font" size="xs" />
          </button>

          <button
            onClick={() => setSyntaxHighlight(!syntaxHighlight)}
            className={`p-1.5 rounded transition-colors ${
              syntaxHighlight
                ? 'text-primary bg-primary/10'
                : 'text-neutral-400 hover:text-white hover:bg-neutral-700'
            }`}
            title="Toggle syntax highlighting"
          >
            <Icon name="magic" size="xs" />
          </button>

          <button
            onClick={() => setIsMinimapVisible(!isMinimapVisible)}
            className={`p-1.5 rounded transition-colors ${
              isMinimapVisible
                ? 'text-primary bg-primary/10'
                : 'text-neutral-400 hover:text-white hover:bg-neutral-700'
            }`}
            title="Toggle minimap"
          >
            <Icon name={isMinimapVisible ? 'compress' : 'expand'} size="xs" />
          </button>
        </div>

        <div className="flex items-center gap-3">
          {/* File info */}
          <div className="text-sm text-neutral-400">
            {getLineCount()} lines • {content.length} characters
          </div>

          {/* Action buttons */}
          <div className="flex items-center gap-2">
            <button
              onClick={handleCopyCode}
              className="p-1.5 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded transition-colors"
              title="Copy code"
            >
              <Icon name="copy" size="xs" />
            </button>

            {isExecutable() && (
              <button
                onClick={handleRun}
                className="px-3 py-1.5 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm flex items-center gap-2"
                title="Run code"
              >
                <Icon name="play" size="xs" />
                Run
              </button>
            )}

            {/* Edit mode controls */}
            {viewMode === 'edit' && (
              <>
                {isModified && (
                  <span className="text-xs text-yellow-400">Modified</span>
                )}

                <button
                  onClick={handleSave}
                  disabled={!isModified || isSaving}
                  className={`
                    px-3 py-1.5 text-sm rounded-lg transition-colors flex items-center gap-2
                    ${isModified && !isSaving
                      ? 'bg-primary text-white hover:bg-primary/90'
                      : 'bg-neutral-700 text-neutral-400 cursor-not-allowed'
                    }
                  `}
                >
                  {isSaving ? (
                    <Icon name="spinner" size="xs" spin />
                  ) : (
                    <Icon name="save" size="xs" />
                  )}
                  Save
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {viewMode === 'preview' ? renderPreview() : renderEditor()}
      </div>

      {/* Status bar */}
      <div className="flex items-center justify-between px-3 py-2 border-t border-neutral-700 bg-neutral-800/30 text-xs text-neutral-500">
        <div className="flex items-center gap-4">
          <span className="capitalize">{getLanguage()}</span>
          <span>UTF-8</span>
          <span>LF</span>
          {syntaxHighlight && <span>Syntax Highlighting</span>}
        </div>
        
        <div className="flex items-center gap-4">
          {viewMode === 'edit' && (
            <>
              <span>Ln 1, Col 1</span>
              <span>Sel 0</span>
            </>
          )}
          <span>{viewMode === 'edit' ? 'Edit Mode' : 'Preview Mode'}</span>
        </div>
      </div>
    </div>
  )
}

export default CodeViewer
