import React, { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faFile,
  faFileText,
  faFileCode,
  faFileImage,
  faFilePdf,
  faFileWord,
  faFileExcel,
  faFilePowerpoint,
  faFileArchive,
  faFileVideo,
  faFileAudio,
  faFolder,
  faCalendar,
  faWeight,
  faRuler,
  faEye,
  faEdit,
  faPlay,
  faDownload,
  faShare,
  faInfoCircle,
  faSpinner,
  faExclamationTriangle,
  faImage,
  faCode,
  faFileLines,
  faClock,
  faUser,
  faTag,
  faTimes
} from '@fortawesome/free-solid-svg-icons'
import { FileTreeNode } from '../types'

interface FilePreviewPanelProps {
  selectedFile: FileTreeNode | null
  onAction: (action: string, file: FileTreeNode) => void
  className?: string
}

interface FileMetadata {
  size: number
  created: string
  modified: string
  type: string
  dimensions?: { width: number; height: number }
  duration?: number
  encoding?: string
  permissions?: string
  hash?: string
}

interface PreviewContent {
  type: 'text' | 'image' | 'code' | 'pdf' | 'archive' | 'metadata' | 'error'
  content: string
  metadata?: FileMetadata
  error?: string
}

const FilePreviewPanel: React.FC<FilePreviewPanelProps> = ({
  selectedFile,
  onAction,
  className = ''
}) => {
  const [previewContent, setPreviewContent] = useState<PreviewContent | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [previewMode, setPreviewMode] = useState<'preview' | 'metadata'>('preview')

  useEffect(() => {
    if (selectedFile && selectedFile.type === 'file') {
      loadPreview(selectedFile)
    } else {
      setPreviewContent(null)
    }
  }, [selectedFile])

  const loadPreview = async (file: FileTreeNode) => {
    setIsLoading(true)
    try {
      // Simulate loading preview content
      // In real implementation, this would call the backend
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const fileExtension = file.name.split('.').pop()?.toLowerCase() || ''
      const mockMetadata: FileMetadata = {
        size: Math.floor(Math.random() * 1000000) + 1000,
        created: new Date(Date.now() - Math.random() * 10000000000).toISOString(),
        modified: new Date(Date.now() - Math.random() * 1000000000).toISOString(),
        type: getFileTypeDescription(fileExtension),
        permissions: 'rw-r--r--'
      }

      if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(fileExtension)) {
        setPreviewContent({
          type: 'image',
          content: `data:image/svg+xml;base64,${btoa(`
            <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
              <rect width="200" height="150" fill="#374151"/>
              <text x="100" y="75" text-anchor="middle" fill="#9CA3AF" font-family="Arial" font-size="14">
                Image Preview
              </text>
              <text x="100" y="95" text-anchor="middle" fill="#6B7280" font-family="Arial" font-size="12">
                ${file.name}
              </text>
            </svg>
          `)}`,
          metadata: {
            ...mockMetadata,
            dimensions: { width: 1920, height: 1080 }
          }
        })
      } else if (['txt', 'md', 'log', 'json', 'xml', 'csv', 'yaml', 'yml'].includes(fileExtension)) {
        setPreviewContent({
          type: 'text',
          content: `# ${file.name}\n\nThis is a preview of the text file content.\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n\n## Features\n- Text preview\n- Syntax highlighting\n- Line numbers\n- Search functionality\n\n\`\`\`javascript\nfunction example() {\n  console.log("Hello, World!");\n}\n\`\`\``,
          metadata: mockMetadata
        })
      } else if (['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'scala', 'html', 'css', 'scss', 'less'].includes(fileExtension)) {
        setPreviewContent({
          type: 'code',
          content: `// ${file.name}\n\nfunction exampleFunction() {\n  const message = "Hello, World!";\n  console.log(message);\n  \n  return {\n    success: true,\n    data: message,\n    timestamp: new Date().toISOString()\n  };\n}\n\n// Export the function\nexport default exampleFunction;`,
          metadata: mockMetadata
        })
      } else if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].includes(fileExtension)) {
        setPreviewContent({
          type: 'archive',
          content: JSON.stringify({
            files: [
              { name: 'README.md', size: 1024, type: 'file' },
              { name: 'src/', size: 0, type: 'folder' },
              { name: 'src/index.js', size: 2048, type: 'file' },
              { name: 'src/components/', size: 0, type: 'folder' },
              { name: 'src/components/App.jsx', size: 4096, type: 'file' },
              { name: 'package.json', size: 512, type: 'file' }
            ],
            totalFiles: 4,
            totalFolders: 2,
            compressedSize: mockMetadata.size,
            uncompressedSize: mockMetadata.size * 3
          }),
          metadata: mockMetadata
        })
      } else {
        setPreviewContent({
          type: 'metadata',
          content: '',
          metadata: mockMetadata
        })
      }
    } catch (error) {
      setPreviewContent({
        type: 'error',
        content: '',
        error: 'Failed to load preview'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getFileTypeDescription = (extension: string): string => {
    const types: Record<string, string> = {
      txt: 'Plain Text Document',
      md: 'Markdown Document',
      js: 'JavaScript File',
      ts: 'TypeScript File',
      jsx: 'React Component',
      tsx: 'React TypeScript Component',
      py: 'Python Script',
      java: 'Java Source File',
      cpp: 'C++ Source File',
      c: 'C Source File',
      cs: 'C# Source File',
      php: 'PHP Script',
      rb: 'Ruby Script',
      go: 'Go Source File',
      rs: 'Rust Source File',
      html: 'HTML Document',
      css: 'Stylesheet',
      json: 'JSON Data',
      xml: 'XML Document',
      pdf: 'PDF Document',
      jpg: 'JPEG Image',
      jpeg: 'JPEG Image',
      png: 'PNG Image',
      gif: 'GIF Image',
      svg: 'SVG Vector Image',
      mp4: 'MP4 Video',
      avi: 'AVI Video',
      mov: 'QuickTime Video',
      mp3: 'MP3 Audio',
      wav: 'WAV Audio',
      zip: 'ZIP Archive',
      rar: 'RAR Archive',
      '7z': '7-Zip Archive'
    }
    return types[extension] || 'Unknown File Type'
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getFileIcon = (file: FileTreeNode) => {
    if (file.type === 'folder') return faFolder
    
    const extension = file.name.split('.').pop()?.toLowerCase() || ''
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico'].includes(extension)) return faFileImage
    if (['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'scala', 'html', 'css', 'scss', 'less'].includes(extension)) return faFileCode
    if (['txt', 'md', 'log', 'json', 'xml', 'csv', 'yaml', 'yml'].includes(extension)) return faFileText
    if (extension === 'pdf') return faFilePdf
    if (['doc', 'docx'].includes(extension)) return faFileWord
    if (['xls', 'xlsx'].includes(extension)) return faFileExcel
    if (['ppt', 'pptx'].includes(extension)) return faFilePowerpoint
    if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].includes(extension)) return faFileArchive
    if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].includes(extension)) return faFileVideo
    if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma'].includes(extension)) return faFileAudio
    
    return faFile
  }

  const renderPreviewContent = () => {
    if (!previewContent) return null

    switch (previewContent.type) {
      case 'image':
        return (
          <div className="space-y-4">
            <div className="bg-neutral-900 rounded-lg p-4 flex items-center justify-center min-h-48">
              <img 
                src={previewContent.content} 
                alt={selectedFile?.name}
                className="max-w-full max-h-48 object-contain rounded"
              />
            </div>
            {previewContent.metadata?.dimensions && (
              <div className="text-sm text-neutral-400 text-center">
                {previewContent.metadata.dimensions.width} × {previewContent.metadata.dimensions.height} pixels
              </div>
            )}
          </div>
        )

      case 'text':
      case 'code':
        return (
          <div className="bg-neutral-900 rounded-lg p-4 font-mono text-sm">
            <pre className="whitespace-pre-wrap text-neutral-300 overflow-auto max-h-64">
              {previewContent.content}
            </pre>
          </div>
        )

      case 'archive':
        const archiveData = JSON.parse(previewContent.content)
        return (
          <div className="space-y-4">
            <div className="bg-neutral-900 rounded-lg p-4">
              <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                <div>
                  <span className="text-neutral-400">Files:</span>
                  <span className="ml-2 text-white">{archiveData.totalFiles}</span>
                </div>
                <div>
                  <span className="text-neutral-400">Folders:</span>
                  <span className="ml-2 text-white">{archiveData.totalFolders}</span>
                </div>
                <div>
                  <span className="text-neutral-400">Compressed:</span>
                  <span className="ml-2 text-white">{formatFileSize(archiveData.compressedSize)}</span>
                </div>
                <div>
                  <span className="text-neutral-400">Uncompressed:</span>
                  <span className="ml-2 text-white">{formatFileSize(archiveData.uncompressedSize)}</span>
                </div>
              </div>
              <div className="border-t border-neutral-700 pt-4">
                <div className="text-sm font-medium text-neutral-300 mb-2">Contents:</div>
                <div className="space-y-1 max-h-32 overflow-auto">
                  {archiveData.files.map((file: any, index: number) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <FontAwesomeIcon 
                        icon={file.type === 'folder' ? faFolder : faFile} 
                        className="w-3 h-3 text-neutral-500" 
                      />
                      <span className="text-neutral-300">{file.name}</span>
                      {file.type === 'file' && (
                        <span className="text-neutral-500 ml-auto">{formatFileSize(file.size)}</span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )

      case 'error':
        return (
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 text-center">
            <FontAwesomeIcon icon={faExclamationTriangle} className="w-8 h-8 text-red-400 mb-2" />
            <div className="text-red-300">{previewContent.error}</div>
          </div>
        )

      default:
        return (
          <div className="bg-neutral-900 rounded-lg p-8 text-center">
            <FontAwesomeIcon icon={getFileIcon(selectedFile!)} className="w-12 h-12 text-neutral-500 mb-4" />
            <div className="text-neutral-400">No preview available</div>
            <div className="text-sm text-neutral-500 mt-1">Use "Open" to view this file</div>
          </div>
        )
    }
  }

  const renderMetadata = () => {
    if (!previewContent?.metadata) return null

    const metadata = previewContent.metadata

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 gap-3">
          <div className="flex items-center justify-between py-2 border-b border-neutral-700">
            <span className="text-neutral-400 flex items-center gap-2">
              <FontAwesomeIcon icon={faTag} className="w-4 h-4" />
              Type
            </span>
            <span className="text-white">{metadata.type}</span>
          </div>
          
          <div className="flex items-center justify-between py-2 border-b border-neutral-700">
            <span className="text-neutral-400 flex items-center gap-2">
              <FontAwesomeIcon icon={faWeight} className="w-4 h-4" />
              Size
            </span>
            <span className="text-white">{formatFileSize(metadata.size)}</span>
          </div>

          <div className="flex items-center justify-between py-2 border-b border-neutral-700">
            <span className="text-neutral-400 flex items-center gap-2">
              <FontAwesomeIcon icon={faCalendar} className="w-4 h-4" />
              Created
            </span>
            <span className="text-white text-sm">{formatDate(metadata.created)}</span>
          </div>

          <div className="flex items-center justify-between py-2 border-b border-neutral-700">
            <span className="text-neutral-400 flex items-center gap-2">
              <FontAwesomeIcon icon={faClock} className="w-4 h-4" />
              Modified
            </span>
            <span className="text-white text-sm">{formatDate(metadata.modified)}</span>
          </div>

          {metadata.dimensions && (
            <div className="flex items-center justify-between py-2 border-b border-neutral-700">
              <span className="text-neutral-400 flex items-center gap-2">
                <FontAwesomeIcon icon={faRuler} className="w-4 h-4" />
                Dimensions
              </span>
              <span className="text-white">{metadata.dimensions.width} × {metadata.dimensions.height}</span>
            </div>
          )}

          {metadata.permissions && (
            <div className="flex items-center justify-between py-2">
              <span className="text-neutral-400 flex items-center gap-2">
                <FontAwesomeIcon icon={faUser} className="w-4 h-4" />
                Permissions
              </span>
              <span className="text-white font-mono text-sm">{metadata.permissions}</span>
            </div>
          )}
        </div>
      </div>
    )
  }

  if (!selectedFile) {
    return (
      <div className={`flex-1 flex items-center justify-center bg-neutral-900/50 ${className}`}>
        <div className="text-center">
          <FontAwesomeIcon icon={faEye} className="w-12 h-12 text-neutral-600 mb-4" />
          <div className="text-neutral-400 text-lg mb-2">No file selected</div>
          <div className="text-neutral-500 text-sm">Select a file to see its preview and details</div>
        </div>
      </div>
    )
  }

  if (selectedFile.type === 'folder') {
    return (
      <div className={`flex-1 flex items-center justify-center bg-neutral-900/50 ${className}`}>
        <div className="text-center">
          <FontAwesomeIcon icon={faFolder} className="w-12 h-12 text-primary mb-4" />
          <div className="text-neutral-300 text-lg mb-2">{selectedFile.name}</div>
          <div className="text-neutral-500 text-sm">Folder • {selectedFile.children?.length || 0} items</div>
        </div>
      </div>
    )
  }

  return (
    <div className={`flex-1 flex flex-col bg-neutral-900/50 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-neutral-700">
        <div className="flex items-center gap-3 mb-3">
          <FontAwesomeIcon
            icon={getFileIcon(selectedFile)}
            className="w-5 h-5 text-primary"
          />
          <div className="flex-1 min-w-0">
            <div className="text-white font-medium truncate">{selectedFile.name}</div>
            <div className="text-sm text-neutral-400">{selectedFile.path}</div>
          </div>
          <button
            onClick={() => onAction('close-preview', selectedFile)}
            className="p-1 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded transition-colors"
            title="Close Preview"
          >
            <FontAwesomeIcon icon={faTimes} className="w-4 h-4" />
          </button>
        </div>

        {/* Mode toggle */}
        <div className="flex bg-neutral-800 rounded-lg p-1">
          <button
            onClick={() => setPreviewMode('preview')}
            className={`flex-1 px-3 py-1.5 text-sm rounded-md transition-colors ${
              previewMode === 'preview'
                ? 'bg-neutral-700 text-white'
                : 'text-neutral-400 hover:text-white'
            }`}
          >
            <FontAwesomeIcon icon={faEye} className="w-3 h-3 mr-2" />
            Preview
          </button>
          <button
            onClick={() => setPreviewMode('metadata')}
            className={`flex-1 px-3 py-1.5 text-sm rounded-md transition-colors ${
              previewMode === 'metadata'
                ? 'bg-neutral-700 text-white'
                : 'text-neutral-400 hover:text-white'
            }`}
          >
            <FontAwesomeIcon icon={faInfoCircle} className="w-3 h-3 mr-2" />
            Details
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-4 overflow-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <FontAwesomeIcon icon={faSpinner} className="w-6 h-6 text-primary animate-spin" />
          </div>
        ) : previewMode === 'preview' ? (
          renderPreviewContent()
        ) : (
          renderMetadata()
        )}
      </div>

      {/* Quick actions */}
      <div className="p-4 border-t border-neutral-700">
        <div className="flex gap-2">
          <button
            onClick={() => onAction('open-primary', selectedFile)}
            className="flex-1 px-3 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors text-sm"
          >
            <FontAwesomeIcon icon={faPlay} className="w-3 h-3 mr-2" />
            Open
          </button>
          <button
            onClick={() => onAction('quick-preview', selectedFile)}
            className="px-3 py-2 bg-neutral-700 text-neutral-300 rounded-lg hover:bg-neutral-600 transition-colors text-sm"
          >
            <FontAwesomeIcon icon={faEye} className="w-3 h-3" />
          </button>
          <button
            onClick={() => onAction('download', selectedFile)}
            className="px-3 py-2 bg-neutral-700 text-neutral-300 rounded-lg hover:bg-neutral-600 transition-colors text-sm"
          >
            <FontAwesomeIcon icon={faDownload} className="w-3 h-3" />
          </button>
          <button
            onClick={() => onAction('share', selectedFile)}
            className="px-3 py-2 bg-neutral-700 text-neutral-300 rounded-lg hover:bg-neutral-600 transition-colors text-sm"
          >
            <FontAwesomeIcon icon={faShare} className="w-3 h-3" />
          </button>
        </div>
      </div>
    </div>
  )
}

export default FilePreviewPanel
