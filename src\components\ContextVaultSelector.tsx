import React, { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faFolder, faChevronDown, faPlus } from '@fortawesome/free-solid-svg-icons'
import { ContextVaultCard } from '../types'
import { vaultUIManager } from '../services/vaultUIManager'

interface ContextVaultSelectorProps {
  selectedContextId?: string
  onContextSelect: (contextId: string | null) => void
  className?: string
}

const ContextVaultSelector: React.FC<ContextVaultSelectorProps> = ({
  selectedContextId,
  onContextSelect,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [contexts, setContexts] = useState<ContextVaultCard[]>([])
  const [loading, setLoading] = useState(false)

  // Load available contexts
  useEffect(() => {
    loadContexts()
  }, [])

  const loadContexts = async () => {
    try {
      setLoading(true)
      const cards = await vaultUIManager.getVaultCards()
      setContexts(cards)
    } catch (error) {
      console.error('Error loading contexts:', error)
    } finally {
      setLoading(false)
    }
  }

  const selectedContext = contexts.find(c => c.id === selectedContextId)

  const handleContextSelect = (contextId: string | null) => {
    onContextSelect(contextId)
    setIsOpen(false)
  }

  return (
    <div className={`relative ${className}`}>
      {/* Trigger Button */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-1.5 bg-gray-800 border border-tertiary/50 rounded-lg text-sm hover:bg-gray-700 transition-colors"
      >
        <FontAwesomeIcon icon={faFolder} className="text-primary text-xs" />
        <span className="text-supplement1 truncate max-w-32">
          {selectedContext ? selectedContext.name : 'No Context Vault Selected'}
        </span>
        <FontAwesomeIcon 
          icon={faChevronDown} 
          className={`text-gray-400 text-xs transition-transform ${isOpen ? 'rotate-180' : ''}`} 
        />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-gray-900 border border-tertiary/50 rounded-lg shadow-xl z-50 min-w-64">
          {/* Header */}
          <div className="p-3 border-b border-tertiary/30">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-supplement1">Context Vault</span>
              <button
                onClick={() => {
                  // TODO: Navigate to create new context
                  console.log('Create new context')
                  setIsOpen(false)
                }}
                className="text-xs text-primary hover:text-primary/80 flex items-center gap-1"
              >
                <FontAwesomeIcon icon={faPlus} />
                New
              </button>
            </div>
            <p className="text-xs text-gray-400 mt-1">
              Select a context vault for this conversation
            </p>
          </div>

          {/* Context List */}
          <div className="max-h-64 overflow-y-auto">
            {/* No Context Option */}
            <div
              className={`p-3 cursor-pointer hover:bg-gray-800 transition-colors ${
                !selectedContextId ? 'bg-primary/10 border-l-2 border-primary' : ''
              }`}
              onClick={() => handleContextSelect(null)}
            >
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-gray-700 rounded flex items-center justify-center">
                  <FontAwesomeIcon icon={faFolder} className="text-gray-400 text-xs" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-supplement1">No Context Vault Selected</p>
                  <p className="text-xs text-gray-400">Chat without context memory</p>
                </div>
              </div>
            </div>

            {/* Loading State */}
            {loading && (
              <div className="p-3 text-center">
                <p className="text-sm text-gray-400">Loading contexts...</p>
              </div>
            )}

            {/* Context Options */}
            {contexts.map((context) => (
              <div
                key={context.id}
                className={`p-3 cursor-pointer hover:bg-gray-800 transition-colors ${
                  selectedContextId === context.id ? 'bg-primary/10 border-l-2 border-primary' : ''
                }`}
                onClick={() => handleContextSelect(context.id)}
              >
                <div className="flex items-center gap-2">
                  <div 
                    className="w-6 h-6 rounded flex items-center justify-center"
                    style={{ backgroundColor: context.color + '20' }}
                  >
                    <FontAwesomeIcon 
                      icon={faFolder} 
                      className="text-xs"
                      style={{ color: context.color }}
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-supplement1 truncate">{context.name}</p>
                    <div className="flex items-center gap-3 text-xs text-gray-400">
                      <span>{context.fileCount} files</span>
                      <span>{context.conversationCount} chats</span>
                      <span className="truncate">{context.lastActivity}</span>
                    </div>
                  </div>
                  {selectedContextId === context.id && (
                    <div className="w-2 h-2 bg-primary rounded-full" />
                  )}
                </div>
              </div>
            ))}

            {/* Empty State */}
            {!loading && contexts.length === 0 && (
              <div className="p-6 text-center">
                <FontAwesomeIcon icon={faFolder} className="text-gray-500 text-2xl mb-2" />
                <p className="text-sm text-gray-400 mb-2">No context vaults found</p>
                <p className="text-xs text-gray-500">Create your first vault in Settings</p>
              </div>
            )}
          </div>

          {/* Footer */}
          {contexts.length > 0 && (
            <div className="p-3 border-t border-tertiary/30">
              <p className="text-xs text-gray-400 text-center">
                💡 Context vaults help AI remember your project files and conversations
              </p>
            </div>
          )}
        </div>
      )}

      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}

export default ContextVaultSelector
